/**
 * 车载音频系统服务
 * 提供专业的车载音频功能，包括均衡器、音效、声场控制等
 */

// 均衡器预设类型
export enum EqualizerPreset {
  NORMAL = 'normal',      // 正常
  ROCK = 'rock',          // 摇滚
  POP = 'pop',            // 流行
  JAZZ = 'jazz',          // 爵士
  CLASSICAL = 'classical', // 古典
  VOCAL = 'vocal',        // 人声
  BASS = 'bass',          // 重低音
  CUSTOM = 'custom'       // 自定义
}

// 声场模式
export enum SoundFieldMode {
  STEREO = 'stereo',      // 立体声
  SURROUND = 'surround',  // 环绕声
  CONCERT = 'concert',    // 音乐厅
  STADIUM = 'stadium',    // 体育场
  CHURCH = 'church',      // 教堂
  LIVE = 'live'          // 现场
}

// 音频增强选项
export interface AudioEnhancement {
  bassBoost: number;      // 低音增强 (0-100)
  trebleBoost: number;    // 高音增强 (0-100)
  virtualSurround: boolean; // 虚拟环绕声
  loudnessCompensation: boolean; // 响度补偿
  dynamicRangeControl: boolean; // 动态范围控制
}

// 车载音频配置
export interface CarAudioConfig {
  equalizerPreset: EqualizerPreset;
  soundFieldMode: SoundFieldMode;
  enhancement: AudioEnhancement;
  balance: number;        // 左右平衡 (-50 到 50)
  fade: number;          // 前后平衡 (-50 到 50)
  speedVolumeControl: boolean; // 车速音量控制
  autoVolumeLevel: number; // 自动音量级别 (0-10)
}

export class CarAudioService {
  private static instance: CarAudioService;
  private config: CarAudioConfig;
  private listeners: Array<(config: CarAudioConfig) => void> = [];

  private constructor() {
    // 默认车载音频配置
    this.config = {
      equalizerPreset: EqualizerPreset.NORMAL,
      soundFieldMode: SoundFieldMode.STEREO,
      enhancement: {
        bassBoost: 0,
        trebleBoost: 0,
        virtualSurround: false,
        loudnessCompensation: true,
        dynamicRangeControl: false
      },
      balance: 0,
      fade: 0,
      speedVolumeControl: false,
      autoVolumeLevel: 5
    };
  }

  public static getInstance(): CarAudioService {
    if (!CarAudioService.instance) {
      CarAudioService.instance = new CarAudioService();
    }
    return CarAudioService.instance;
  }

  /**
   * 获取当前音频配置
   */
  public getConfig(): CarAudioConfig {
    return {
      equalizerPreset: this.config.equalizerPreset,
      soundFieldMode: this.config.soundFieldMode,
      enhancement: {
        bassBoost: this.config.enhancement.bassBoost,
        trebleBoost: this.config.enhancement.trebleBoost,
        virtualSurround: this.config.enhancement.virtualSurround,
        loudnessCompensation: this.config.enhancement.loudnessCompensation,
        dynamicRangeControl: this.config.enhancement.dynamicRangeControl
      },
      balance: this.config.balance,
      fade: this.config.fade,
      speedVolumeControl: this.config.speedVolumeControl,
      autoVolumeLevel: this.config.autoVolumeLevel
    };
  }

  /**
   * 设置均衡器预设
   */
  public setEqualizerPreset(preset: EqualizerPreset): void {
    console.info('🎚️ 设置均衡器预设:', preset);
    this.config.equalizerPreset = preset;
    this.applyEqualizerPreset(preset);
    this.notifyListeners();
  }

  /**
   * 设置声场模式
   */
  public setSoundFieldMode(mode: SoundFieldMode): void {
    console.info('🎭 设置声场模式:', mode);
    this.config.soundFieldMode = mode;
    this.applySoundFieldMode(mode);
    this.notifyListeners();
  }

  /**
   * 设置音频增强
   */
  public setAudioEnhancement(enhancement: Partial<AudioEnhancement>): void {
    console.info('🔊 设置音频增强:', enhancement);
    if (enhancement.bassBoost !== undefined) {
      this.config.enhancement.bassBoost = enhancement.bassBoost;
    }
    if (enhancement.trebleBoost !== undefined) {
      this.config.enhancement.trebleBoost = enhancement.trebleBoost;
    }
    if (enhancement.virtualSurround !== undefined) {
      this.config.enhancement.virtualSurround = enhancement.virtualSurround;
    }
    if (enhancement.loudnessCompensation !== undefined) {
      this.config.enhancement.loudnessCompensation = enhancement.loudnessCompensation;
    }
    if (enhancement.dynamicRangeControl !== undefined) {
      this.config.enhancement.dynamicRangeControl = enhancement.dynamicRangeControl;
    }
    this.applyAudioEnhancement();
    this.notifyListeners();
  }

  /**
   * 设置左右平衡
   */
  public setBalance(balance: number): void {
    this.config.balance = Math.max(-50, Math.min(50, balance));
    console.info('⚖️ 设置左右平衡:', this.config.balance);
    this.notifyListeners();
  }

  /**
   * 设置前后平衡
   */
  public setFade(fade: number): void {
    this.config.fade = Math.max(-50, Math.min(50, fade));
    console.info('🔄 设置前后平衡:', this.config.fade);
    this.notifyListeners();
  }

  /**
   * 获取均衡器预设显示名称
   */
  public getEqualizerPresetName(preset: EqualizerPreset): string {
    switch (preset) {
      case EqualizerPreset.NORMAL:
        return '正常';
      case EqualizerPreset.ROCK:
        return '摇滚';
      case EqualizerPreset.POP:
        return '流行';
      case EqualizerPreset.JAZZ:
        return '爵士';
      case EqualizerPreset.CLASSICAL:
        return '古典';
      case EqualizerPreset.VOCAL:
        return '人声';
      case EqualizerPreset.BASS:
        return '重低音';
      case EqualizerPreset.CUSTOM:
        return '自定义';
      default:
        return '未知';
    }
  }

  /**
   * 获取声场模式显示名称
   */
  public getSoundFieldModeName(mode: SoundFieldMode): string {
    switch (mode) {
      case SoundFieldMode.STEREO:
        return '立体声';
      case SoundFieldMode.SURROUND:
        return '环绕声';
      case SoundFieldMode.CONCERT:
        return '音乐厅';
      case SoundFieldMode.STADIUM:
        return '体育场';
      case SoundFieldMode.CHURCH:
        return '教堂';
      case SoundFieldMode.LIVE:
        return '现场';
      default:
        return '未知';
    }
  }

  /**
   * 添加配置变化监听器
   */
  public addConfigListener(listener: (config: CarAudioConfig) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除配置变化监听器
   */
  public removeConfigListener(listener: (config: CarAudioConfig) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 应用均衡器预设
   */
  private applyEqualizerPreset(preset: EqualizerPreset): void {
    // 这里可以调用HarmonyOS的音频API来应用均衡器设置
    // 目前只是模拟实现
    console.info('🎚️ 应用均衡器预设:', preset);
  }

  /**
   * 应用声场模式
   */
  private applySoundFieldMode(mode: SoundFieldMode): void {
    // 这里可以调用HarmonyOS的音频API来应用声场设置
    // 目前只是模拟实现
    console.info('🎭 应用声场模式:', mode);
  }

  /**
   * 应用音频增强设置
   */
  private applyAudioEnhancement(): void {
    // 这里可以调用HarmonyOS的音频API来应用音频增强
    // 目前只是模拟实现
    console.info('🔊 应用音频增强:', this.config.enhancement);
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getConfig());
      } catch (error) {
        console.error('通知音频配置监听器失败:', error);
      }
    });
  }

  /**
   * 重置为默认配置
   */
  public resetToDefault(): void {
    console.info('🔄 重置音频配置为默认值');
    this.config = {
      equalizerPreset: EqualizerPreset.NORMAL,
      soundFieldMode: SoundFieldMode.STEREO,
      enhancement: {
        bassBoost: 0,
        trebleBoost: 0,
        virtualSurround: false,
        loudnessCompensation: true,
        dynamicRangeControl: false
      },
      balance: 0,
      fade: 0,
      speedVolumeControl: false,
      autoVolumeLevel: 5
    };
    this.notifyListeners();
  }

  /**
   * 获取推荐的均衡器预设（基于当前播放的音乐类型）
   */
  public getRecommendedPreset(musicGenre?: string): EqualizerPreset {
    if (!musicGenre) return EqualizerPreset.NORMAL;
    
    const genreLower = musicGenre.toLowerCase();
    if (genreLower.includes('rock') || genreLower.includes('摇滚')) {
      return EqualizerPreset.ROCK;
    } else if (genreLower.includes('pop') || genreLower.includes('流行')) {
      return EqualizerPreset.POP;
    } else if (genreLower.includes('jazz') || genreLower.includes('爵士')) {
      return EqualizerPreset.JAZZ;
    } else if (genreLower.includes('classical') || genreLower.includes('古典')) {
      return EqualizerPreset.CLASSICAL;
    } else if (genreLower.includes('vocal') || genreLower.includes('人声')) {
      return EqualizerPreset.VOCAL;
    }
    
    return EqualizerPreset.NORMAL;
  }
}
