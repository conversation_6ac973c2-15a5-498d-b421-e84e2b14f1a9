@echo off
chcp 65001 >nul
echo ========================================
echo 🎵 创建HarmonyOS音乐文件夹结构
echo ========================================
echo.

echo 正在创建音乐文件夹结构...

REM 创建音乐文件夹
if not exist "entry\src\main\resources\rawfile" (
    mkdir "entry\src\main\resources\rawfile"
    echo ✓ 创建 rawfile 文件夹
)

if not exist "entry\src\main\resources\rawfile\music" (
    mkdir "entry\src\main\resources\rawfile\music"
    echo ✓ 创建 music 文件夹
)

REM 检查media文件夹是否存在
if not exist "entry\src\main\resources\base\media" (
    mkdir "entry\src\main\resources\base\media"
    echo ✓ 创建 media 文件夹
)

echo.
echo ========================================
echo 📁 文件夹结构创建完成！
echo ========================================
echo.
echo 现在您需要下载以下音乐文件：
echo.
echo 🎵 需要下载的音乐列表：
echo ================================
echo 1. 夜空中最亮的星 - 逃跑计划
echo    文件名：song1.mp3
echo    存放位置：entry\src\main\resources\rawfile\music\song1.mp3
echo.
echo 2. 成都 - 赵雷
echo    文件名：song2.mp3
echo    存放位置：entry\src\main\resources\rawfile\music\song2.mp3
echo.
echo 3. 告白气球 - 周杰伦
echo    文件名：song3.mp3
echo    存放位置：entry\src\main\resources\rawfile\music\song3.mp3
echo.
echo 4. 演员 - 薛之谦
echo    文件名：song4.mp3
echo    存放位置：entry\src\main\resources\rawfile\music\song4.mp3
echo.
echo 5. 稻香 - 周杰伦
echo    文件名：song5.mp3
echo    存放位置：entry\src\main\resources\rawfile\music\song5.mp3
echo.
echo 🖼️ 封面图片：
echo    已配置为网络加载，无需手动下载
echo    系统会自动从酷狗音乐服务器获取封面
echo.
echo 📝 音乐文件要求：
echo    - 格式：MP3 (推荐)
echo    - 质量：128-320kbps
echo    - 大小：3-10MB每首
echo.
echo ========================================
echo 🎯 下一步操作：
echo ========================================
echo.
echo 1. 将您的音乐文件复制到 rawfile\music\ 文件夹
echo 2. 将对应的封面图片复制到 base\media\ 文件夹
echo 3. 在DevEco Studio中编译并运行项目
echo 4. 进入音乐页面测试播放功能
echo.
echo 详细说明请查看：🎵音乐文件放置指南.md
echo.
pause
