// 导入HarmonyOS应用开发所需的核心模块
import { AbilityConstant, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';

// 定义日志域标识符，用于日志输出的分类
const DOMAIN = 0x0000;

/**
 * 应用主入口Ability类
 * 继承自UIAbility，负责管理应用的生命周期和窗口
 */
export default class EntryAbility extends UIAbility {
  /**
   * Ability创建时的回调函数
   * @param want - 启动意图，包含启动参数
   * @param launchParam - 启动参数，包含启动模式等信息
   */
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 设置应用颜色模式为系统默认（不强制设置深色或浅色模式）
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    // 记录Ability创建日志
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
  }

  /**
   * Ability销毁时的回调函数
   * 用于清理资源和保存数据
   */
  onDestroy(): void {
    // 记录Ability销毁日志
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  /**
   * 窗口舞台创建时的回调函数
   * 在此函数中加载主页面
   * @param windowStage - 窗口舞台对象，用于管理窗口
   */
  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 主窗口已创建，为此Ability设置主页面
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    // 加载Index页面作为应用的主界面
    windowStage.loadContent('pages/Index', (err) => {
      if (err.code) {
        // 页面加载失败时记录错误日志
        hilog.error(DOMAIN, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err));
        return;
      }
      // 页面加载成功时记录成功日志
      hilog.info(DOMAIN, 'testTag', 'Succeeded in loading the content.');
    });
  }

  /**
   * 窗口舞台销毁时的回调函数
   * 用于释放UI相关资源
   */
  onWindowStageDestroy(): void {
    // 主窗口被销毁，释放UI相关资源
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  /**
   * Ability切换到前台时的回调函数
   * 应用从后台切换到前台时触发
   */
  onForeground(): void {
    // Ability已切换到前台
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onForeground');
  }

  /**
   * Ability切换到后台时的回调函数
   * 应用从前台切换到后台时触发
   */
  onBackground(): void {
    // Ability已切换到后台
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onBackground');
  }
}