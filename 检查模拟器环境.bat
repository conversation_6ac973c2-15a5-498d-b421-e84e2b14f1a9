@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 HarmonyOS模拟器环境检查
echo ========================================
echo.

:: 检查DevEco Studio相关命令
echo 📋 检查开发工具...
echo.

echo 1. 检查 ohpm 命令：
where ohpm >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ ohpm 命令可用
    for /f "tokens=*" %%i in ('ohpm --version 2^>nul') do echo    版本：%%i
) else (
    echo    ❌ ohpm 命令不可用
    echo    💡 请安装 DevEco Studio 并配置环境变量
)
echo.

echo 2. 检查 hdc 命令：
where hdc >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ hdc 命令可用
    for /f "tokens=*" %%i in ('hdc version 2^>nul') do echo    版本：%%i
) else (
    echo    ❌ hdc 命令不可用
    echo    💡 请确保 HarmonyOS SDK 工具链已正确安装
)
echo.

echo 3. 检查 hvigor 构建工具：
if exist "hvigorw.bat" (
    echo    ✅ hvigorw.bat 存在
) else (
    echo    ❌ hvigorw.bat 不存在
    echo    💡 请确保在正确的 HarmonyOS 项目目录中运行
)
echo.

:: 检查项目结构
echo 📁 检查项目结构...
echo.

if exist "entry" (
    echo    ✅ entry 目录存在
) else (
    echo    ❌ entry 目录不存在
)

if exist "entry\src\main\ets" (
    echo    ✅ 源码目录存在
) else (
    echo    ❌ 源码目录不存在
)

if exist "entry\src\main\module.json5" (
    echo    ✅ 模块配置文件存在
) else (
    echo    ❌ 模块配置文件不存在
)

if exist "oh-package.json5" (
    echo    ✅ 包配置文件存在
) else (
    echo    ❌ 包配置文件不存在
)
echo.

:: 检查设备连接
echo 📱 检查设备连接状态...
echo.

hdc list targets > temp_devices.txt 2>&1
set "DEVICE_COUNT=0"
set "EMULATOR_COUNT=0"
set "REAL_DEVICE_COUNT=0"

echo 连接的设备列表：
for /f "tokens=*" %%i in (temp_devices.txt) do (
    set "line=%%i"
    if not "!line!"=="" (
        echo    - %%i
        set /a DEVICE_COUNT+=1
        echo %%i | findstr /i "emulator" >nul
        if !errorlevel! equ 0 (
            set /a EMULATOR_COUNT+=1
        ) else (
            set /a REAL_DEVICE_COUNT+=1
        )
    )
)

del temp_devices.txt >nul 2>&1

echo.
echo 设备统计：
echo    📱 总设备数：%DEVICE_COUNT%
echo    🖥️ 模拟器数：%EMULATOR_COUNT%
echo    📲 真机数：%REAL_DEVICE_COUNT%
echo.

if %EMULATOR_COUNT% gtr 0 (
    echo ✅ 检测到模拟器，可以进行部署
) else (
    echo ⚠️ 未检测到模拟器
    echo.
    echo 💡 启动模拟器的步骤：
    echo 1. 打开 DevEco Studio
    echo 2. 点击 Tools → Device Manager
    echo 3. 如果没有模拟器，点击 "New Emulator" 创建：
    echo    - 设备类型：Phone
    echo    - 系统版本：HarmonyOS 3.0+ (API 9+)
    echo    - 内存：4GB (推荐)
    echo    - 存储：32GB
    echo 4. 点击 "Start" 启动模拟器
    echo 5. 等待模拟器完全启动（显示桌面）
    echo 6. 重新运行此检查脚本
)
echo.

:: 检查网络连接
echo 🌐 检查网络连接...
echo.

ping -n 1 www.baidu.com >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 网络连接正常
    echo    💡 网络音乐功能可以正常使用
) else (
    echo    ⚠️ 网络连接异常
    echo    💡 网络音乐功能可能受影响
)
echo.

:: 检查项目依赖
echo 📦 检查项目依赖...
echo.

if exist "oh_modules" (
    echo    ✅ 依赖目录存在
    echo    💡 如果需要重新安装依赖，运行：ohpm install
) else (
    echo    ⚠️ 依赖目录不存在
    echo    💡 请运行：ohpm install
)

if exist "oh-package-lock.json5" (
    echo    ✅ 依赖锁定文件存在
) else (
    echo    ⚠️ 依赖锁定文件不存在
)
echo.

:: 检查构建产物
echo 🔨 检查构建状态...
echo.

set "HAP_FILE=entry\build\default\outputs\default\entry-default-signed.hap"
if exist "%HAP_FILE%" (
    echo    ✅ HAP文件存在
    for %%A in ("%HAP_FILE%") do (
        echo    📦 文件大小：%%~zA 字节
        echo    📅 修改时间：%%~tA
    )
    echo    💡 可以直接部署，或运行构建命令更新
) else (
    echo    ⚠️ HAP文件不存在
    echo    💡 需要先构建项目：hvigorw assembleHap
)
echo.

:: 系统要求检查
echo 💻 检查系统要求...
echo.

echo    操作系统：
for /f "tokens=*" %%i in ('ver') do echo    %%i

echo    内存信息：
wmic computersystem get TotalPhysicalMemory /format:value | findstr "TotalPhysicalMemory" >temp_memory.txt
for /f "tokens=2 delims==" %%i in (temp_memory.txt) do (
    set /a MEMORY_GB=%%i/1024/1024/1024
    echo    总内存：!MEMORY_GB! GB
)
del temp_memory.txt >nul 2>&1

if %MEMORY_GB% geq 8 (
    echo    ✅ 内存充足，适合运行模拟器
) else (
    echo    ⚠️ 内存较少，模拟器可能运行缓慢
)
echo.

:: 显示总结
echo ========================================
echo 📊 环境检查总结
echo ========================================
echo.

set "READY_TO_DEPLOY=true"

if %errorlevel% neq 0 (
    set "READY_TO_DEPLOY=false"
)

where ohpm >nul 2>&1
if %errorlevel% neq 0 (
    set "READY_TO_DEPLOY=false"
)

where hdc >nul 2>&1
if %errorlevel% neq 0 (
    set "READY_TO_DEPLOY=false"
)

if %EMULATOR_COUNT% equ 0 (
    set "READY_TO_DEPLOY=false"
)

if "%READY_TO_DEPLOY%"=="true" (
    echo 🎉 环境检查通过！
    echo.
    echo ✅ 开发工具已安装
    echo ✅ 项目结构正确
    echo ✅ 模拟器已连接
    echo ✅ 网络连接正常
    echo.
    echo 🚀 您可以开始部署项目：
    echo    1. 双击运行 "模拟器部署.bat"
    echo    2. 或在 DevEco Studio 中点击 Run 按钮
    echo.
) else (
    echo ⚠️ 环境检查发现问题
    echo.
    echo 💡 请解决以下问题后重新检查：
    
    where ohpm >nul 2>&1
    if %errorlevel% neq 0 (
        echo    - 安装 DevEco Studio 并配置环境变量
    )
    
    where hdc >nul 2>&1
    if %errorlevel% neq 0 (
        echo    - 配置 HarmonyOS SDK 工具链
    )
    
    if %EMULATOR_COUNT% equ 0 (
        echo    - 创建并启动 HarmonyOS 模拟器
    )
    
    echo.
    echo 📚 详细解决方案请参考：📱模拟器部署指南.md
)

echo.
echo 🔧 可用的部署脚本：
echo    - 模拟器部署.bat     （自动部署到模拟器）
echo    - 快速启动.bat       （部署到任何设备）
echo    - 检查模拟器环境.bat （当前脚本）
echo.

pause
