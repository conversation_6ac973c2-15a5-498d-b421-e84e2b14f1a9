/**
 * 音乐播放历史记录服务
 * 提供播放历史、最近播放、播放统计等功能
 */

import { MusicInfo } from '../common/MusicTypes';

// 播放历史记录项
export interface PlayHistoryItem {
  music: MusicInfo;
  playTime: number;        // 播放时间戳
  playDuration: number;    // 播放时长（毫秒）
  isCompleted: boolean;    // 是否播放完成
  skipReason?: string;     // 跳过原因（如果有）
}

// 播放统计信息
export interface PlayStatistics {
  totalPlayTime: number;      // 总播放时长（毫秒）
  totalSongs: number;         // 总播放歌曲数
  favoriteGenre: string;      // 最喜欢的音乐类型
  mostPlayedSong: MusicInfo | null; // 最常播放的歌曲
  averagePlayTime: number;    // 平均播放时长
  todayPlayTime: number;      // 今日播放时长
  weekPlayTime: number;       // 本周播放时长
}

// 推荐原因
export enum RecommendReason {
  RECENTLY_PLAYED = 'recently_played',    // 最近播放
  FREQUENTLY_PLAYED = 'frequently_played', // 经常播放
  SIMILAR_GENRE = 'similar_genre',        // 相似类型
  SAME_ARTIST = 'same_artist',           // 相同艺术家
  LISTENING_HABIT = 'listening_habit'     // 听歌习惯
}

// 推荐音乐项
export interface RecommendedMusic {
  music: MusicInfo;
  reason: RecommendReason;
  score: number;           // 推荐分数 (0-100)
  reasonText: string;      // 推荐原因文本
}

export class MusicHistoryService {
  private static instance: MusicHistoryService;
  private playHistory: PlayHistoryItem[] = [];
  private maxHistorySize: number = 1000; // 最大历史记录数量
  private listeners: Array<(history: PlayHistoryItem[]) => void> = [];

  private constructor() {
    this.loadHistoryFromStorage();
  }

  public static getInstance(): MusicHistoryService {
    if (!MusicHistoryService.instance) {
      MusicHistoryService.instance = new MusicHistoryService();
    }
    return MusicHistoryService.instance;
  }

  /**
   * 添加播放记录
   */
  public addPlayRecord(music: MusicInfo, playDuration: number, isCompleted: boolean, skipReason?: string): void {
    const historyItem: PlayHistoryItem = {
      music: music,
      playTime: Date.now(),
      playDuration: playDuration,
      isCompleted: isCompleted,
      skipReason: skipReason
    };

    // 添加到历史记录开头
    this.playHistory.unshift(historyItem);

    // 限制历史记录数量
    if (this.playHistory.length > this.maxHistorySize) {
      this.playHistory = this.playHistory.slice(0, this.maxHistorySize);
    }

    console.info('🎵 添加播放记录:', music.title, '播放时长:', this.formatDuration(playDuration));
    
    this.saveHistoryToStorage();
    this.notifyListeners();
  }

  /**
   * 获取播放历史
   */
  public getPlayHistory(limit?: number): PlayHistoryItem[] {
    return limit ? this.playHistory.slice(0, limit) : [...this.playHistory];
  }

  /**
   * 获取最近播放的音乐（去重）
   */
  public getRecentlyPlayed(limit: number = 20): MusicInfo[] {
    const recentMusic: MusicInfo[] = [];
    const addedIds = new Set<string>();

    for (const item of this.playHistory) {
      if (!addedIds.has(item.music.id) && recentMusic.length < limit) {
        recentMusic.push(item.music);
        addedIds.add(item.music.id);
      }
    }

    return recentMusic;
  }

  /**
   * 获取播放统计信息
   */
  public getPlayStatistics(): PlayStatistics {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const oneWeekMs = 7 * oneDayMs;

    let totalPlayTime = 0;
    let todayPlayTime = 0;
    let weekPlayTime = 0;
    const songPlayCount = new Map<string, number>();
    const genreCount = new Map<string, number>();

    for (const item of this.playHistory) {
      totalPlayTime += item.playDuration;
      
      // 今日播放时长
      if (now - item.playTime < oneDayMs) {
        todayPlayTime += item.playDuration;
      }
      
      // 本周播放时长
      if (now - item.playTime < oneWeekMs) {
        weekPlayTime += item.playDuration;
      }

      // 歌曲播放次数统计
      const songId = item.music.id;
      songPlayCount.set(songId, (songPlayCount.get(songId) || 0) + 1);

      // 音乐类型统计
      const genre = item.music.album || '未知';
      genreCount.set(genre, (genreCount.get(genre) || 0) + 1);
    }

    // 找出最常播放的歌曲
    let mostPlayedSong: MusicInfo | null = null;
    let maxPlayCount = 0;
    songPlayCount.forEach((count: number, songId: string) => {
      if (count > maxPlayCount) {
        maxPlayCount = count;
        const historyItem = this.playHistory.find(item => item.music.id === songId);
        if (historyItem) {
          mostPlayedSong = historyItem.music;
        }
      }
    });

    // 找出最喜欢的音乐类型
    let favoriteGenre = '未知';
    let maxGenreCount = 0;
    genreCount.forEach((count: number, genre: string) => {
      if (count > maxGenreCount) {
        maxGenreCount = count;
        favoriteGenre = genre;
      }
    });

    return {
      totalPlayTime,
      totalSongs: this.playHistory.length,
      favoriteGenre,
      mostPlayedSong,
      averagePlayTime: this.playHistory.length > 0 ? totalPlayTime / this.playHistory.length : 0,
      todayPlayTime,
      weekPlayTime
    };
  }

  /**
   * 获取推荐音乐
   */
  public getRecommendedMusic(currentMusic: MusicInfo | null, allMusic: MusicInfo[], limit: number = 10): RecommendedMusic[] {
    const recommendations: RecommendedMusic[] = [];
    const statistics = this.getPlayStatistics();
    const recentlyPlayed = this.getRecentlyPlayed(50);
    const recentIds = new Set(recentlyPlayed.map(m => m.id));

    for (const music of allMusic) {
      // 跳过当前播放的音乐
      if (currentMusic && music.id === currentMusic.id) continue;

      let score = 0;
      let reason = RecommendReason.LISTENING_HABIT;
      let reasonText = '根据您的听歌习惯推荐';

      // 最近播放过的音乐加分
      if (recentIds.has(music.id)) {
        score += 30;
        reason = RecommendReason.RECENTLY_PLAYED;
        reasonText = '您最近播放过这首歌';
      }

      // 相同艺术家加分
      if (currentMusic && music.artist === currentMusic.artist) {
        score += 25;
        reason = RecommendReason.SAME_ARTIST;
        reasonText = `与当前播放的${currentMusic.artist}是同一艺术家`;
      }

      // 相似音乐类型加分
      if (music.album === statistics.favoriteGenre) {
        score += 20;
        reason = RecommendReason.SIMILAR_GENRE;
        reasonText = `${statistics.favoriteGenre}是您最喜欢的音乐类型`;
      }

      // 播放频率加分
      const playCount = this.getPlayCount(music.id);
      if (playCount > 0) {
        score += Math.min(playCount * 5, 25);
        if (playCount >= 3) {
          reason = RecommendReason.FREQUENTLY_PLAYED;
          reasonText = `您已播放${playCount}次，似乎很喜欢`;
        }
      }

      // 随机因子，增加推荐多样性
      score += Math.random() * 10;

      if (score > 0) {
        recommendations.push({
          music,
          reason,
          score,
          reasonText
        });
      }
    }

    // 按分数排序并返回指定数量
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 获取指定音乐的播放次数
   */
  public getPlayCount(musicId: string): number {
    return this.playHistory.filter(item => item.music.id === musicId).length;
  }

  /**
   * 清空播放历史
   */
  public clearHistory(): void {
    this.playHistory = [];
    this.saveHistoryToStorage();
    this.notifyListeners();
    console.info('🗑️ 播放历史已清空');
  }

  /**
   * 添加历史变化监听器
   */
  public addHistoryListener(listener: (history: PlayHistoryItem[]) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除历史变化监听器
   */
  public removeHistoryListener(listener: (history: PlayHistoryItem[]) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 格式化播放时长
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * 从存储加载历史记录
   */
  private loadHistoryFromStorage(): void {
    try {
      // 这里可以从HarmonyOS的首选项或数据库加载
      // 目前使用模拟数据
      console.info('📚 加载播放历史记录');
    } catch (error) {
      console.error('加载播放历史失败:', error);
    }
  }

  /**
   * 保存历史记录到存储
   */
  private saveHistoryToStorage(): void {
    try {
      // 这里可以保存到HarmonyOS的首选项或数据库
      // 目前只是模拟实现
      console.info('💾 保存播放历史记录');
    } catch (error) {
      console.error('保存播放历史失败:', error);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener([...this.playHistory]);
      } catch (error) {
        console.error('通知历史记录监听器失败:', error);
      }
    });
  }
}
