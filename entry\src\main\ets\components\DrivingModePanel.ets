/**
 * 车载驾驶模式安全控制面板
 * 提供驾驶时的简化控制界面和安全功能
 */

import { MusicInfo, PlayState, PlayMode } from '../common/MusicTypes';

// 驾驶模式配置
export interface DrivingModeConfig {
  isDrivingMode: boolean;           // 是否启用驾驶模式
  simplifiedControls: boolean;      // 简化控制界面
  voiceControlEnabled: boolean;     // 语音控制启用
  largeButtonMode: boolean;         // 大按钮模式
  autoVolumeAdjust: boolean;        // 自动音量调节
  speedBasedVolume: boolean;        // 基于速度的音量调节
  safetyReminders: boolean;         // 安全提醒
  nightMode: boolean;               // 夜间模式
}

@Component
export struct DrivingModePanel {
  @State private drivingConfig: DrivingModeConfig = {
    isDrivingMode: false,
    simplifiedControls: true,
    voiceControlEnabled: false,
    largeButtonMode: true,
    autoVolumeAdjust: true,
    speedBasedVolume: false,
    safetyReminders: true,
    nightMode: false
  };

  // 当前播放状态
  @Prop currentMusic: MusicInfo | null;
  @Prop playState: PlayState;
  @Prop volume: number;

  // 回调函数
  onPlayPause?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onVolumeChange?: (volume: number) => void;
  onConfigChange?: (config: DrivingModeConfig) => void;

  build() {
    Column() {
      // 驾驶模式标题和开关
      Row() {
        Column() {
          Text('🚗 驾驶模式')
            .fontColor('#00D4FF')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
          
          Text('为驾驶安全优化的控制界面')
            .fontColor('#CCCCCC')
            .fontSize(12)
            .opacity(0.8)
            .margin({ top: 2 })
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)
        
        Toggle({ type: ToggleType.Switch, isOn: this.drivingConfig.isDrivingMode })
          .selectedColor('#00D4FF')
          .switchPointColor('#FFFFFF')
          .onChange((isOn: boolean) => {
            this.drivingConfig.isDrivingMode = isOn;
            this.notifyConfigChange();
          })
      }
      .width('100%')
      .padding({ left: 15, right: 15, top: 12, bottom: 12 })
      .backgroundColor('rgba(0, 212, 255, 0.1)')
      .borderRadius(12)
      .margin({ bottom: 20 })

      if (this.drivingConfig.isDrivingMode) {
        // 驾驶模式激活时的界面
        this.buildDrivingInterface()
      } else {
        // 驾驶模式配置界面
        this.buildConfigInterface()
      }
    }
    .width('100%')
  }

  @Builder
  buildDrivingInterface() {
    Column() {
      // 安全提醒
      if (this.drivingConfig.safetyReminders) {
        Row() {
          Text('⚠️')
            .fontSize(16)
            .margin({ right: 8 })
          
          Text('请专注驾驶，注意行车安全')
            .fontColor('#FFB84D')
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
        }
        .width('100%')
        .padding(12)
        .backgroundColor('rgba(255, 184, 77, 0.15)')
        .borderRadius(10)
        .margin({ bottom: 20 })
      }

      // 当前播放信息（大字体显示）
      if (this.currentMusic) {
        Column() {
          Text(this.currentMusic.title)
            .fontColor('#FFFFFF')
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .width('100%')
            .textAlign(TextAlign.Center)
          
          Text(this.currentMusic.artist)
            .fontColor('#CCCCCC')
            .fontSize(16)
            .margin({ top: 8 })
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .width('100%')
            .textAlign(TextAlign.Center)
        }
        .width('100%')
        .padding(20)
        .backgroundColor('rgba(255, 255, 255, 0.05)')
        .borderRadius(15)
        .margin({ bottom: 25 })
      }

      // 大按钮控制区域
      if (this.drivingConfig.largeButtonMode) {
        Row() {
          // 上一首按钮
          this.buildLargeControlButton('⏮️', '上一首', () => {
            if (this.onPrevious) this.onPrevious();
          })
          
          Blank()
          
          // 播放/暂停按钮
          this.buildLargeControlButton(
            this.playState === PlayState.PLAYING ? '⏸️' : '▶️',
            this.playState === PlayState.PLAYING ? '暂停' : '播放',
            () => {
              if (this.onPlayPause) this.onPlayPause();
            }
          )
          
          Blank()
          
          // 下一首按钮
          this.buildLargeControlButton('⏭️', '下一首', () => {
            if (this.onNext) this.onNext();
          })
        }
        .width('100%')
        .margin({ bottom: 25 })
      }

      // 音量控制（大滑块）
      Column() {
        Text(`🔊 音量 ${this.volume}%`)
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 15 })
        
        Slider({
          value: this.volume,
          min: 0,
          max: 100,
          style: SliderStyle.OutSet
        })
          .trackColor('#333333')
          .selectedColor('#00D4FF')
          .blockColor('#FFFFFF')
          .width('100%')
          .height(50) // 更大的滑块，便于驾驶时操作
          .onChange((value: number) => {
            if (this.onVolumeChange) {
              this.onVolumeChange(Math.round(value));
            }
          })
      }
      .width('100%')
      .padding(15)
      .backgroundColor('rgba(0, 212, 255, 0.08)')
      .borderRadius(12)
      .margin({ bottom: 20 })

      // 语音控制提示
      if (this.drivingConfig.voiceControlEnabled) {
        Row() {
          Text('🎤')
            .fontSize(18)
            .margin({ right: 10 })
          
          Column() {
            Text('语音控制已启用')
              .fontColor('#00D4FF')
              .fontSize(14)
              .fontWeight(FontWeight.Bold)
            
            Text('说"播放"、"暂停"、"下一首"、"上一首"')
              .fontColor('#CCCCCC')
              .fontSize(12)
              .opacity(0.8)
              .margin({ top: 2 })
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)
        }
        .width('100%')
        .padding(12)
        .backgroundColor('rgba(0, 212, 255, 0.1)')
        .borderRadius(10)
      }
    }
  }

  @Builder
  buildLargeControlButton(icon: string, label: string, onClick: () => void) {
    Column() {
      Text(icon)
        .fontSize(32)
        .margin({ bottom: 8 })
      
      Text(label)
        .fontColor('#CCCCCC')
        .fontSize(12)
        .opacity(0.8)
    }
    .width(80)
    .height(80)
    .justifyContent(FlexAlign.Center)
    .backgroundColor('rgba(255, 255, 255, 0.1)')
    .borderRadius(40)
    .border({ width: 2, color: 'rgba(0, 212, 255, 0.3)' })
    .onClick(onClick)
    .gesture(
      TapGesture()
        .onAction(() => {
          // 触觉反馈（如果支持）
          console.info('🎯 驾驶模式按钮点击:', label);
        })
    )
  }

  @Builder
  buildConfigInterface() {
    Column() {
      Text('🔧 驾驶模式配置')
        .fontColor('#00D4FF')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 15 })
      
      // 配置选项列表
      this.buildConfigOption('简化控制界面', '减少复杂操作，专注核心功能', this.drivingConfig.simplifiedControls, (value: boolean) => {
        this.drivingConfig.simplifiedControls = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('大按钮模式', '增大按钮尺寸，便于驾驶时操作', this.drivingConfig.largeButtonMode, (value: boolean) => {
        this.drivingConfig.largeButtonMode = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('语音控制', '启用语音命令控制音乐播放', this.drivingConfig.voiceControlEnabled, (value: boolean) => {
        this.drivingConfig.voiceControlEnabled = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('自动音量调节', '根据环境噪音自动调节音量', this.drivingConfig.autoVolumeAdjust, (value: boolean) => {
        this.drivingConfig.autoVolumeAdjust = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('速度音量调节', '根据车速自动调节音量大小', this.drivingConfig.speedBasedVolume, (value: boolean) => {
        this.drivingConfig.speedBasedVolume = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('安全提醒', '显示驾驶安全提醒信息', this.drivingConfig.safetyReminders, (value: boolean) => {
        this.drivingConfig.safetyReminders = value;
        this.notifyConfigChange();
      })
      
      this.buildConfigOption('夜间模式', '自动调整界面亮度和对比度', this.drivingConfig.nightMode, (value: boolean) => {
        this.drivingConfig.nightMode = value;
        this.notifyConfigChange();
      })
    }
  }

  @Builder
  buildConfigOption(title: string, description: string, isEnabled: boolean, onChange: (value: boolean) => void) {
    Row() {
      Column() {
        Text(title)
          .fontColor('#FFFFFF')
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
        
        Text(description)
          .fontColor('#CCCCCC')
          .fontSize(12)
          .opacity(0.7)
          .margin({ top: 2 })
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
      
      Toggle({ type: ToggleType.Switch, isOn: isEnabled })
        .selectedColor('#00D4FF')
        .switchPointColor('#FFFFFF')
        .onChange(onChange)
    }
    .width('100%')
    .padding({ left: 12, right: 12, top: 10, bottom: 10 })
    .backgroundColor('rgba(255, 255, 255, 0.03)')
    .borderRadius(8)
    .margin({ bottom: 8 })
  }

  private notifyConfigChange(): void {
    if (this.onConfigChange) {
      this.onConfigChange({
        isDrivingMode: this.drivingConfig.isDrivingMode,
        simplifiedControls: this.drivingConfig.simplifiedControls,
        voiceControlEnabled: this.drivingConfig.voiceControlEnabled,
        largeButtonMode: this.drivingConfig.largeButtonMode,
        autoVolumeAdjust: this.drivingConfig.autoVolumeAdjust,
        speedBasedVolume: this.drivingConfig.speedBasedVolume,
        safetyReminders: this.drivingConfig.safetyReminders,
        nightMode: this.drivingConfig.nightMode
      });
    }
  }
}
