/**
 * 音乐播放相关的数据类型定义
 */

/**
 * 音乐信息接口
 */
export interface MusicInfo {
  id: string;           // 音乐唯一标识
  title: string;        // 歌曲名称
  artist: string;       // 艺术家
  album?: string;       // 专辑名称
  duration: number;     // 时长（毫秒）
  uri: string;          // 文件路径或URL（支持网络URL）
  coverUri?: string;    // 封面图片路径或URL（支持网络URL）
  isLiked: boolean;     // 是否点赞
  isFavorite: boolean;  // 是否收藏
  playCount: number;    // 播放次数
  isNetworkResource?: boolean;  // 是否为网络资源
}

/**
 * 播放状态枚举
 */
export enum PlayState {
  STOPPED = 'stopped',   // 停止
  PLAYING = 'playing',   // 播放中
  PAUSED = 'paused',     // 暂停
  LOADING = 'loading',   // 加载中
  ERROR = 'error'        // 错误
}

/**
 * 播放模式枚举
 */
export enum PlayMode {
  SEQUENCE = 'sequence',    // 顺序播放
  LOOP = 'loop',           // 列表循环
  SINGLE = 'single',       // 单曲循环
  RANDOM = 'random'        // 随机播放
}

/**
 * 播放器事件类型
 */
export enum PlayerEvent {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  NEXT = 'next',
  PREVIOUS = 'previous',
  SEEK = 'seek',
  TIME_UPDATE = 'timeUpdate',
  DURATION_CHANGE = 'durationChange',
  STATE_CHANGE = 'stateChange',
  ERROR = 'error',
  // 兼容性事件名称
  STATE_CHANGED = 'stateChange',
  MUSIC_CHANGED = 'musicChanged',
  POSITION_CHANGED = 'positionChanged',
  DURATION_CHANGED = 'durationChange',
  VOLUME_CHANGE = 'volumeChange',
  PLAY_MODE_CHANGED = 'playModeChanged',
  VOLUME_CHANGED = 'volumeChanged',
  LOADING_CHANGED = 'loadingChanged'
}

/**
 * 播放器状态信息
 */
export interface PlayerState {
  currentMusic: MusicInfo | null;
  playState: PlayState;
  playMode: PlayMode;
  currentPosition: number;  // 当前播放位置（毫秒）
  duration: number;         // 总时长（毫秒）
  volume: number;           // 音量 (0-100)
  isLoading: boolean;       // 是否正在加载
}

/**
 * 播放列表信息
 */
export interface PlaylistInfo {
  id: string;
  name: string;
  description?: string;
  coverUri?: string;
  musicList: MusicInfo[];
  createTime: number;
  updateTime: number;
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  playMode: PlayMode;
  volume: number;
  favoriteList: string[];    // 收藏的歌曲ID列表
  likedList: string[];       // 点赞的歌曲ID列表
  recentPlayList: string[];  // 最近播放的歌曲ID列表
  lastPlayedId?: string;     // 最后播放的歌曲ID
  playHistory: string[];     // 播放历史记录
}
