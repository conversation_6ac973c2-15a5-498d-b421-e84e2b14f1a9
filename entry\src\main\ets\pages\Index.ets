// 导入系统日期时间模块，用于获取系统时间
import systemDateTime from '@ohos.systemDateTime';
// 导入设备工具类
import { DeviceUtils, DeviceType } from '../common/DeviceUtils';
// 导入音乐播放相关模块
import { MusicPlayerService } from '../services/MusicPlayerService';
import { MusicDataService } from '../services/MusicDataService';
import { MusicInfo, PlayState, PlayMode, PlayerEvent } from '../common/MusicTypes';
import { common } from '@kit.AbilityKit';
import { CarAudioControlPanel } from '../components/CarAudioControlPanel';
import { MusicRecommendationPanel } from '../components/MusicRecommendationPanel';
import { DrivingModePanel, DrivingModeConfig } from '../components/DrivingModePanel';
import { AudioTestUtil } from '../utils/AudioTestUtil';

/**
 * 应用主页面组件
 * @Entry 表示这是应用的入口页面
 * @Component 表示这是一个自定义组件
 */
@Entry
@Component
struct Index {
  /**
   * 自定义构建器函数，用于创建带图标的TabBar
   * @param tupian - 图片资源路径字符串
   * @returns 返回配置好的Image组件
   */
  @Builder imageBuilder(tupian: string) {
    Image($r(tupian))
      .width(50)
  }

  // 状态变量：左侧滑块的数值（温度控制）
  @State sliderValue: number = 30;
  // 状态变量：右侧滑块的数值（温度控制）
  @State sliderValue1: number = 30;
  // 状态变量：当前时间字符串显示
  @State timeStr: string = "加载中...";
  // 私有变量：定时器ID，用于定时更新时间
  private timer: number = 0;

  // 音乐播放相关状态变量
  @State currentMusic: MusicInfo | null = null;        // 当前播放的音乐
  @State playState: PlayState = PlayState.STOPPED;     // 播放状态
  @State playMode: PlayMode = PlayMode.SEQUENCE;       // 播放模式
  @State currentPosition: number = 0;                  // 当前播放位置（毫秒）
  @State duration: number = 0;                         // 总时长（毫秒）
  @State volume: number = 50;                          // 音量
  @State isLoading: boolean = false;                   // 是否正在加载
  @State playlist: MusicInfo[] = [];                   // 播放列表
  @State isLiked: boolean = false;                     // 当前歌曲是否点赞
  @State isFavorite: boolean = false;                  // 当前歌曲是否收藏
  @State showAudioSettings: boolean = false;           // 是否显示音频设置面板
  @State showRecommendations: boolean = false;         // 是否显示音乐推荐面板
  @State showDrivingMode: boolean = false;             // 是否显示驾驶模式面板
  @State drivingModeConfig: DrivingModeConfig = {       // 驾驶模式配置
    isDrivingMode: false,
    simplifiedControls: true,
    voiceControlEnabled: false,
    largeButtonMode: true,
    autoVolumeAdjust: true,
    speedBasedVolume: false,
    safetyReminders: true,
    nightMode: false
  };

  // 音乐服务实例
  private musicPlayer: MusicPlayerService = new MusicPlayerService();
  private musicDataService: MusicDataService = new MusicDataService();
  private context: common.UIAbilityContext | null = null;

  /**
   * 获取格式化时间字符串的异步方法
   * 优先使用系统时间API，失败时回退到JavaScript Date对象
   * @returns Promise<string> - 返回格式化的时间字符串 "YYYY-MM-DD HH:mm:ss"
   */
  private async getFormattedTime(): Promise<string> {
    try {
      let timestamp: number;
      try {
        // 尝试使用HarmonyOS系统时间API获取时间戳
        timestamp = await systemDateTime.getTime();
        console.info(`systemDateTime时间戳: ${timestamp}`);
      } catch (e) {
        // 系统API失败时，使用JavaScript原生Date.now()作为备选方案
        console.warn(`使用systemDateTime失败，回退到Date.now()`);
        timestamp = Date.now();
      }

      // 验证时间戳的合理性（2020年9月之后的时间戳）
      if (timestamp < 1600000000000) {
        console.error(`异常时间戳: ${timestamp}，使用Date.now()`);
        timestamp = Date.now();
      }

      // 将时间戳转换为Date对象并格式化
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      // 返回格式化的时间字符串
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    } catch (error) {
      // 捕获所有异常并返回错误提示
      console.error(`获取时间失败: ${(error as Error).message}`);
      return '时间获取失败';
    }
  }

  /**
   * 启动定时器更新时间显示
   * 每秒钟更新一次时间字符串
   */
  private startTimer() {
    // 先清除可能存在的旧定时器，避免重复创建定时器
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 立即更新一次时间，避免等待1秒后才显示时间
    this.getFormattedTime().then(time => {
      this.timeStr = time;
    });

    // 创建定时器，每秒更新一次时间显示
    this.timer = setInterval(async () => {
      this.timeStr = await this.getFormattedTime();
    }, 1000);
  }

  /**
   * 清除定时器
   * 用于组件销毁时释放定时器资源
   */
  private clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = 0;
    }
  }

  /**
   * 组件即将出现时的生命周期回调
   * 在组件挂载到组件树之前调用
   */
  aboutToAppear() {
    // 启动时间更新定时器
    this.startTimer();
    // 初始化音乐播放功能
    this.initMusicPlayer();
  }

  /**
   * 组件即将消失时的生命周期回调
   * 在组件从组件树中卸载之前调用
   */
  aboutToDisappear() {
    // 清除定时器，释放资源
    this.clearTimer();
    // 释放音乐播放器资源
    this.releaseMusicPlayer();
  }

  /**
   * 构建UI界面的方法
   * 创建车载娱乐系统的主界面，包含多个功能标签页和控制组件
   */
  build() {
    Column() {
      // 优化后的顶部状态栏
      Row() {
        // 左侧温度控制区域
        Row() {
          Image($r('app.media.shezhi'))
            .width(24)
            .height(24)
            .margin({ right: 8 })
          Slider({
            value: this.sliderValue,
            min: 0,
            max: 100,
            style: SliderStyle.InSet
          })
            .blockColor('#FFFFFF')
            .trackColor('#666666')
            .selectedColor('#FF6B35')
            .width(80)
            .height(30)
            .onChange((value: number) => {
              this.sliderValue = value;
            })
          Text(`${Math.round(this.sliderValue)}°C`)
            .fontColor(Color.White)
            .fontSize(14)
            .margin({ left: 8 })
        }
        .alignItems(VerticalAlign.Center)
        .padding({ left: 15, right: 10 })

        Blank()

        // 中央车辆状态指示器
        Row() {
          Image($r('app.media.car'))
            .width(40)
            .height(40)
            .borderRadius(20)
            .backgroundColor('#4CAF50')
            .padding(8)
        }
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)

        Blank()

        // 音乐快捷控制区域
        Row() {
          Image($r('app.media.yinyue'))
            .width(24)
            .height(24)
            .margin({ right: 8 })
        }
        .alignItems(VerticalAlign.Center)

        Blank()

        // 右侧温度控制区域
        Row() {
          Image($r('app.media.shezhi'))
            .width(24)
            .height(24)
            .margin({ right: 8 })
          Slider({
            value: this.sliderValue1,
            min: 0,
            max: 100,
            style: SliderStyle.InSet
          })
            .blockColor('#FFFFFF')
            .trackColor('#666666')
            .selectedColor('#FF6B35')
            .width(80)
            .height(30)
            .onChange((value: number) => {
              this.sliderValue1 = value;
            })
          Text(`${Math.round(this.sliderValue1)}°C`)
            .fontColor(Color.White)
            .fontSize(14)
            .margin({ left: 8 })
        }
        .alignItems(VerticalAlign.Center)
        .padding({ left: 10, right: 15 })
      }
      .width('100%')
      .height(60)
      .backgroundColor('#FF8C00')
      .position({ top: 0 })
      .zIndex(1000)
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      // 主要的标签页容器，包含多个功能页面
      Tabs() {
        // 第一个标签页：车辆设置页面
        TabContent() {
          Column() {
            // 页面顶部标题栏
            Row() {
              // 左侧：车辆图标和标题
              Row(){
                Image($r("app.media.car"))           // 车辆图标
                  .width('50')
                  .margin({ left: 10 })
                Text('我的车辆设置')                    // 页面标题
                  .fontColor(Color.White)
                  .fontSize(30)
                  .padding({ top: 10, left: 10 })
              }
              // 右侧：实时时间显示
              Text(this.timeStr)
                .fontColor(Color.White)
                .fontSize(16)
                .padding({ top: 18, left: 10 ,right:10})
                .textAlign(TextAlign.End)
            }.justifyContent(FlexAlign.SpaceBetween)    // 左右两端对齐
            .width('100%')
            .height(50)
            .backgroundColor('#ff000000')               // 黑色背景
            .alignItems(VerticalAlign.Top)              // 顶部对齐

            // 快捷设置区域
            Row(){
              Text('快捷设置')
                .fontSize(20)
                .fontColor(Color.White)
            }
          }
          .height('100%')
          .width('100%')
        }
        .tabBar(this.imageBuilder('app.media.shezhi'))  // 设置页面的标签图标
        .backgroundColor(Color.Black)                    // 标签页背景色

        // 第二个标签页：应用中心页面
        TabContent() {
          Row() {
            // 第一列应用图标
            Column() {
              Image($r('app.media.wangyiyun'))          // 网易云音乐图标
                .width(70)
              Image($r('app.media.liulanqi'))           // 浏览器图标
                .width(70)
            }
            .justifyContent(FlexAlign.SpaceEvenly)      // 垂直方向均匀分布
            .height(300)
            .margin({ bottom: 100 })

            // 第二列应用图标
            Column() {
              Image($r('app.media.tou'))                // 头像/用户图标
                .width(70)
              Image($r('app.media.phone'))              // 电话图标
                .width(70)
            }
            .justifyContent(FlexAlign.SpaceEvenly)
            .height(300)
            .margin({ bottom: 100 })

            // 第三列应用图标
            Column() {
              Image($r('app.media.yingyong'))           // 应用商店图标
                .width(70)
              Image($r('app.media.douyin'))             // 抖音图标
                .width(70)
            }
            .justifyContent(FlexAlign.SpaceEvenly)
            .height(300)
            .margin({ bottom: 100 })

            // 第四列应用图标
            Column() {
              Image($r('app.media.weixin'))             // 微信图标
                .width(70)
              Image($r('app.media.ditu'))               // 地图图标
                .width(70)
            }
            .height(300)
            .justifyContent(FlexAlign.SpaceEvenly)
            .margin({ bottom: 100 })
          }
          .width('100%')
          .height('100%')
          .alignItems(VerticalAlign.Center)             // 垂直居中对齐
          .justifyContent(FlexAlign.SpaceEvenly)        // 水平方向均匀分布
          .backgroundColor(Color.Black)                 // 黑色背景
        }
        .tabBar(this.imageBuilder('app.media.yingyong')) // 应用页面的标签图标

        // 第三个标签页：音乐播放页面 - 美化版本
        TabContent() {
          Column(){
            // 🎵 音乐页面主要内容区域 - 现代化设计
            Column() {
              // 🎨 音乐封面和信息显示区域 - 居中布局
              Column() {
                // 🖼️ 大尺寸音乐封面
                if (this.currentMusic && this.currentMusic.coverUri) {
                  if (this.currentMusic.coverUri.startsWith('http')) {
                    // 网络图片 - 高质量显示
                    Image(this.currentMusic.coverUri)
                      .width(200)
                      .height(200)
                      .borderRadius(20)
                      .alt($r('app.media.yinyue')) // 加载失败时的默认图片
                      .objectFit(ImageFit.Cover)
                      .shadow({ radius: 20, color: 'rgba(0, 0, 0, 0.3)', offsetX: 0, offsetY: 8 })
                      .border({ width: 3, color: 'rgba(255, 255, 255, 0.2)' })
                  } else {
                    // 本地资源图片
                    Image($r(this.currentMusic.coverUri))
                      .width(200)
                      .height(200)
                      .borderRadius(20)
                      .objectFit(ImageFit.Cover)
                      .shadow({ radius: 20, color: 'rgba(0, 0, 0, 0.3)', offsetX: 0, offsetY: 8 })
                      .border({ width: 3, color: 'rgba(255, 255, 255, 0.2)' })
                  }
                } else {
                  // 默认封面 - 美化样式
                  Image($r('app.media.yinyue'))
                    .width(200)
                    .height(200)
                    .borderRadius(20)
                    .objectFit(ImageFit.Cover)
                    .shadow({ radius: 20, color: 'rgba(0, 0, 0, 0.3)', offsetX: 0, offsetY: 8 })
                    .border({ width: 3, color: 'rgba(255, 255, 255, 0.2)' })
                    .backgroundColor('rgba(255, 255, 255, 0.1)')
                }

                // 🎵 歌曲信息显示 - 居中对齐
                Column() {
                  Text(this.currentMusic ?
                    `${this.currentMusic.title}` :
                    '暂无播放歌曲')
                    .fontColor('#FFFFFF')
                    .fontSize(28)
                    .fontWeight(FontWeight.Bold)
                    .maxLines(2)
                    .textAlign(TextAlign.Center)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })
                    .margin({ top: 25, bottom: 8 })
                    .shadow({ radius: 5, color: 'rgba(0, 0, 0, 0.5)', offsetX: 0, offsetY: 2 })

                  Text(this.currentMusic ?
                    `${this.currentMusic.artist}` :
                    '未知艺术家')
                    .fontColor('#E0E0E0')
                    .fontSize(18)
                    .fontWeight(FontWeight.Medium)
                    .opacity(0.9)
                    .maxLines(1)
                    .textAlign(TextAlign.Center)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })
                    .margin({ bottom: 5 })

                  // 🎶 播放状态指示器 - 美化样式
                  Row() {
                    Text('🎵')
                      .fontSize(16)
                      .margin({ right: 8 })
                    Text(this.getPlayStateText())
                      .fontColor('#00D4FF')
                      .fontSize(16)
                      .fontWeight(FontWeight.Medium)
                  }
                  .justifyContent(FlexAlign.Center)
                  .margin({ bottom: 20 })
                }
                .width('100%')
                .alignItems(HorizontalAlign.Center)

                // 🎚️ 音量控制区域 - 简化美观设计
                Row() {
                  Text('🔇')
                    .fontSize(20)
                    .opacity(this.volume === 0 ? 1 : 0.5)
                    .margin({ right: 15 })

                  Slider({
                    value: this.volume,
                    min: 0,
                    max: 100,
                    style: SliderStyle.OutSet
                  })
                  .trackColor('rgba(255, 255, 255, 0.3)')
                  .selectedColor('#00D4FF')
                  .blockColor('#FFFFFF')
                  .layoutWeight(1)
                  .height(40)
                  .onChange((value: number) => {
                    this.onVolumeChange(value);
                  })

                  Text('🔊')
                    .fontSize(20)
                    .opacity(this.volume === 100 ? 1 : 0.5)
                    .margin({ left: 15, right: 10 })

                  Text(`${Math.round(this.volume)}%`)
                    .fontColor('#00D4FF')
                    .fontSize(16)
                    .fontWeight(FontWeight.Bold)
                    .width(50)
                    .textAlign(TextAlign.Center)
                }
                .width('100%')
                .alignItems(VerticalAlign.Center)
                .margin({ top: 25, bottom: 15 })
                .padding({ left: 20, right: 20 })
                // 🎮 音乐控制按钮组 - 现代化设计
                Column(){
                  // 主要控制按钮行
                  Row(){
                    // 收藏按钮
                    Column() {
                      Image($r('app.media.xin'))
                        .width(45)
                        .height(45)
                        .fillColor(this.isFavorite ? '#FF6B6B' : '#FFFFFF')
                        .opacity(this.isFavorite ? 1.0 : 0.8)
                      Text('收藏')
                        .fontColor('#FFFFFF')
                        .fontSize(12)
                        .margin({ top: 5 })
                        .opacity(0.8)
                    }
                    .width(70)
                    .alignItems(HorizontalAlign.Center)
                    .onClick(() => {
                      this.onFavoriteClick();
                    })

                    // 上一首按钮
                    Column() {
                      Image($r('app.media.tui'))
                        .width(50)
                        .height(50)
                        .fillColor('#FFFFFF')
                        .opacity(0.9)
                      Text('上一首')
                        .fontColor('#FFFFFF')
                        .fontSize(12)
                        .margin({ top: 5 })
                        .opacity(0.8)
                    }
                    .width(70)
                    .alignItems(HorizontalAlign.Center)
                    .onClick(() => {
                      this.onPreviousClick();
                    })

                    // 播放/暂停按钮 - 突出显示
                    Column() {
                      Image($r(this.getPlayStateIcon()))
                        .width(65)
                        .height(65)
                        .fillColor('#00D4FF')
                        .backgroundColor('rgba(0, 212, 255, 0.2)')
                        .borderRadius(32.5)
                        .padding(10)
                        .shadow({ radius: 15, color: 'rgba(0, 212, 255, 0.4)', offsetX: 0, offsetY: 5 })
                      Text(this.playState === PlayState.PLAYING ? '暂停' : '播放')
                        .fontColor('#00D4FF')
                        .fontSize(12)
                        .fontWeight(FontWeight.Bold)
                        .margin({ top: 8 })
                    }
                    .width(90)
                    .alignItems(HorizontalAlign.Center)
                    .onClick(() => {
                      this.onPlayPauseClick();
                    })

                    // 下一首按钮
                    Column() {
                      Image($r('app.media.jin'))
                        .width(50)
                        .height(50)
                        .fillColor('#FFFFFF')
                        .opacity(0.9)
                      Text('下一首')
                        .fontColor('#FFFFFF')
                        .fontSize(12)
                        .margin({ top: 5 })
                        .opacity(0.8)
                    }
                    .width(70)
                    .alignItems(HorizontalAlign.Center)
                    .onClick(() => {
                      this.onNextClick();
                    })

                    // 播放模式按钮
                    Column() {
                      Image($r(this.getPlayModeIcon()))
                        .width(45)
                        .height(45)
                        .fillColor('#FFFFFF')
                        .opacity(0.8)
                      Text(this.getPlayModeText())
                        .fontColor('#FFFFFF')
                        .fontSize(12)
                        .margin({ top: 5 })
                        .opacity(0.8)
                        .maxLines(1)
                        .textOverflow({ overflow: TextOverflow.Ellipsis })
                    }
                    .width(70)
                    .alignItems(HorizontalAlign.Center)
                    .onClick(() => {
                      this.onPlayModeClick();
                    })
                  }
                  .width('100%')
                  .justifyContent(FlexAlign.SpaceEvenly)
                  .alignItems(VerticalAlign.Center)
                  .margin({ top: 30, bottom: 20 })
                }
                .width('100%')
                .alignItems(HorizontalAlign.Center)

                // 🎵 播放进度条区域 - 美化设计
                Column(){
                  // 时间显示 - 优化样式
                  Row() {
                    Text(this.formatTime(this.currentPosition))
                      .fontColor('#E0E0E0')
                      .fontSize(16)
                      .fontWeight(FontWeight.Medium)
                    Blank()
                    Text(this.formatTime(this.duration))
                      .fontColor('#E0E0E0')
                      .fontSize(16)
                      .fontWeight(FontWeight.Medium)
                  }
                  .width('85%')
                  .margin({ bottom: 12 })

                  // 进度条 - 现代化样式
                  Slider({
                    value: this.currentPosition,
                    min: 0,
                    max: this.duration > 0 ? this.duration : 100000, // 确保有合理的最大值
                    style: SliderStyle.OutSet
                  })
                  .trackColor('rgba(255, 255, 255, 0.3)')
                  .selectedColor('#00D4FF')
                  .blockColor('#FFFFFF')
                  .width('85%')
                  .height(6)
                  .onChange((value: number) => {
                    this.onSeekTo(value);
                  })
                  .shadow({ radius: 8, color: 'rgba(0, 212, 255, 0.3)', offsetX: 0, offsetY: 2 })

                  // 进度百分比显示和调试信息
                  Column() {
                    Text(`${Math.round((this.currentPosition / (this.duration || 1)) * 100)}%`)
                      .fontColor('#00D4FF')
                      .fontSize(14)
                      .fontWeight(FontWeight.Medium)
                      .opacity(0.8)

                    // 调试信息 - 显示具体数值
                    Text(`${Math.round(this.currentPosition/1000)}s / ${Math.round(this.duration/1000)}s`)
                      .fontColor('#888888')
                      .fontSize(12)
                      .margin({ top: 4 })
                      .opacity(0.6)
                  }
                  .margin({ top: 8 })
                }
                .width('100%')
                .alignItems(HorizontalAlign.Center)
                .margin({top: 25, bottom: 15})

                // 车载音频设置面板
                if (this.showAudioSettings) {
                  Column() {
                    CarAudioControlPanel()
                  }
                  .width('100%')
                  .margin({ top: 20 })
                  .padding(15)
                  .backgroundColor('rgba(0, 0, 0, 0.8)')
                  .borderRadius(20)
                  .border({ width: 2, color: 'rgba(0, 212, 255, 0.3)' })
                  .shadow({ radius: 15, color: 'rgba(0, 212, 255, 0.2)', offsetX: 0, offsetY: 5 })
                }

                // 音乐推荐面板
                if (this.showRecommendations) {
                  Column() {
                    MusicRecommendationPanel({
                      onMusicSelect: (music: MusicInfo) => {
                        this.onMusicSelectFromRecommendation(music);
                      }
                    })
                  }
                  .width('100%')
                  .margin({ top: 20 })
                  .padding(15)
                  .backgroundColor('rgba(0, 0, 0, 0.85)')
                  .borderRadius(20)
                  .border({ width: 2, color: 'rgba(0, 212, 255, 0.3)' })
                  .shadow({ radius: 15, color: 'rgba(0, 212, 255, 0.2)', offsetX: 0, offsetY: 5 })
                }

                // 驾驶模式面板
                if (this.showDrivingMode) {
                  Column() {
                    DrivingModePanel({
                      currentMusic: this.currentMusic,
                      playState: this.playState,
                      volume: this.volume,
                      onPlayPause: () => {
                        this.onPlayPauseClick();
                      },
                      onNext: () => {
                        this.onNextClick();
                      },
                      onPrevious: () => {
                        this.onPreviousClick();
                      },
                      onVolumeChange: (volume: number) => {
                        this.onVolumeChange(volume);
                      },
                      onConfigChange: (config: DrivingModeConfig) => {
                        this.onDrivingModeConfigChange(config);
                      }
                    })
                  }
                  .width('100%')
                  .margin({ top: 20 })
                  .padding(15)
                  .backgroundColor('rgba(0, 0, 0, 0.9)')
                  .borderRadius(20)
                  .border({ width: 2, color: 'rgba(0, 212, 255, 0.3)' })
                  .shadow({ radius: 15, color: 'rgba(0, 212, 255, 0.2)', offsetX: 0, offsetY: 5 })
                }

              }
              .width('100%')
              .alignItems(HorizontalAlign.Center)
              .padding({ left: 30, right: 30, top: 40, bottom: 30 })
              .backgroundColor('rgba(0, 0, 0, 0.7)')  // 半透明黑色背景
              .borderRadius(30)                        // 大圆角
              .shadow({ radius: 25, color: 'rgba(0, 0, 0, 0.5)', offsetX: 0, offsetY: 10 }) // 深色阴影
              .border({ width: 2, color: 'rgba(255, 255, 255, 0.1)' }) // 细边框
            }
            .width('100%')
            .height('100%')
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
          }
          .width('100%')
          .height('100%')


        }
        .tabBar(this.imageBuilder('app.media.yinyue'))  // 音乐页面的标签图标
        .backgroundColor(Color.Black)                    // 标签页背景色

        // 第四个标签页：地图页面
        TabContent() {
          Column() {
            Text('导航功能')
              .fontSize(28)
              .fontColor(Color.White)
              .textAlign(TextAlign.Center)
              .margin({ top: 100 })

            Text('导航功能开发中...')
              .fontSize(18)
              .fontColor(Color.Gray)
              .textAlign(TextAlign.Center)
              .margin({ top: 40 })
          }
          .width('100%')
          .height('100%')
          .backgroundColor('#000000')
          .justifyContent(FlexAlign.Center)
        }
        .tabBar(this.imageBuilder('app.media.ditu'))    // 地图页面的标签图标
      }
      .barPosition(BarPosition.End)                     // 标签栏位置：底部
      .width('100%')
      .height('calc(100% - 60px)')                      // 为顶部状态栏留出空间
      .margin({ top: 60 })                              // 顶部边距
      .barBackgroundColor(Color.Orange)                 // 标签栏背景色：橙色



    }
  }

  // ==================== 音乐播放相关方法 ====================

  /**
   * 初始化音乐播放器
   */
  private async initMusicPlayer(): Promise<void> {
    try {
      // 获取应用上下文
      this.context = getContext(this) as common.UIAbilityContext;

      // 初始化数据服务
      this.musicDataService = new MusicDataService(this.context);
      await this.musicDataService.initDataStore();

      // 初始化播放器
      await this.musicPlayer.initPlayer();

      // 设置播放器事件监听
      this.setupPlayerEventListeners();

      // 加载播放列表
      this.loadPlaylist();

      // 加载用户偏好设置
      this.loadUserPreferences();

      // 测试音频播放功能
      await this.testAudioPlayback();

      console.info('音乐播放器初始化完成');
    } catch (error) {
      console.error('初始化音乐播放器失败:', error);
    }
  }

  /**
   * 设置播放器事件监听
   */
  private setupPlayerEventListeners(): void {
    // 播放状态变化
    this.musicPlayer.addEventListener(PlayerEvent.STATE_CHANGE, (state: PlayState) => {
      this.playState = state;
      this.updateCurrentMusicInfo();
    });

    // 时间更新
    this.musicPlayer.addEventListener(PlayerEvent.TIME_UPDATE, (position: number) => {
      this.currentPosition = position;
    });

    // 时长变化
    this.musicPlayer.addEventListener(PlayerEvent.DURATION_CHANGE, (duration: number) => {
      this.duration = duration;
    });

    // 播放完成，自动下一首
    this.musicPlayer.addEventListener(PlayerEvent.NEXT, () => {
      this.updateCurrentMusicInfo();
    });

    // 上一首
    this.musicPlayer.addEventListener(PlayerEvent.PREVIOUS, () => {
      this.updateCurrentMusicInfo();
    });

    // 错误处理
    this.musicPlayer.addEventListener(PlayerEvent.ERROR, (error: Object) => {
      console.error('播放器错误:', error);
      this.isLoading = false;
    });
  }

  /**
   * 加载播放列表
   */
  private loadPlaylist(): void {
    this.playlist = this.musicDataService.getDefaultMusicList();
    this.musicPlayer.setPlaylist(this.playlist);

    // 设置第一首歌为当前歌曲
    if (this.playlist.length > 0) {
      this.currentMusic = this.playlist[0];
      this.updateCurrentMusicInfo();
    }
  }

  /**
   * 加载用户偏好设置
   */
  private loadUserPreferences(): void {
    const prefs = this.musicDataService.getUserPreferences();
    this.playMode = prefs.playMode;
    this.volume = prefs.volume;
    this.musicPlayer.setPlayMode(this.playMode);
    this.musicPlayer.setVolume(this.volume);
  }

  /**
   * 更新当前音乐信息
   */
  private updateCurrentMusicInfo(): void {
    this.currentMusic = this.musicPlayer.getCurrentMusic();
    if (this.currentMusic) {
      this.isLiked = this.musicDataService.isLiked(this.currentMusic.id);
      this.isFavorite = this.musicDataService.isFavorite(this.currentMusic.id);
    }
  }

  /**
   * 测试音频播放功能
   */
  private async testAudioPlayback(): Promise<void> {
    try {
      console.info('🧪 开始测试音频播放功能...');

      // 获取系统音频信息
      await AudioTestUtil.getSystemAudioInfo();

      // 测试第一个音频文件
      const testResult = await AudioTestUtil.testAudioFile('resource://RAWFILE/music/song1.mp3');
      console.info('🧪 音频测试结果:', testResult ? '✅ 成功' : '❌ 失败');

      if (!testResult) {
        console.warn('🧪 音频播放测试失败，可能存在以下问题:');
        console.warn('🧪 1. 音频文件路径不正确');
        console.warn('🧪 2. 音频文件格式不支持');
        console.warn('🧪 3. 系统音频权限不足');
        console.warn('🧪 4. 设备音频硬件问题');
      }

      // 释放测试资源
      await AudioTestUtil.release();
    } catch (error) {
      console.error('🧪 音频播放测试异常:', error);
    }
  }

  /**
   * 释放音乐播放器资源
   */
  private async releaseMusicPlayer(): Promise<void> {
    try {
      await this.musicPlayer.release();
      await this.musicDataService.release();
      await AudioTestUtil.release(); // 释放测试工具资源
      console.info('音乐播放器资源释放完成');
    } catch (error) {
      console.error('释放音乐播放器资源失败:', error);
    }
  }

  // ==================== 音乐控制方法 ====================

  /**
   * 播放/暂停切换
   */
  private async onPlayPauseClick(): Promise<void> {
    try {
      console.info('🎵 播放/暂停按钮点击，当前状态:', this.playState);
      console.info('🎵 当前音乐:', this.currentMusic?.title || '无');

      if (this.playState === PlayState.PLAYING) {
        console.info('🎵 暂停播放');
        await this.musicPlayer.pause();
      } else if (this.playState === PlayState.PAUSED) {
        console.info('🎵 恢复播放');
        await this.musicPlayer.play();
      } else {
        // 如果是停止状态，开始播放当前歌曲
        if (this.currentMusic) {
          console.info('🎵 开始播放音乐:', this.currentMusic.title);
          await this.musicPlayer.loadMusic(this.currentMusic);
          await this.musicPlayer.play();
          // 添加到最近播放
          await this.musicDataService.addToRecentPlay(this.currentMusic.id);
        } else {
          console.warn('🎵 没有可播放的音乐');
          // 如果没有当前音乐，尝试播放第一首
          if (this.playlist.length > 0) {
            console.info('🎵 播放第一首音乐:', this.playlist[0].title);
            this.currentMusic = this.playlist[0];
            await this.musicPlayer.loadMusic(this.currentMusic);
            await this.musicPlayer.play();
            await this.musicDataService.addToRecentPlay(this.currentMusic.id);
          }
        }
      }
    } catch (error) {
      console.error('播放/暂停操作失败:', error);
    }
  }

  /**
   * 上一首
   */
  private async onPreviousClick(): Promise<void> {
    try {
      console.info('🎵 点击上一首');
      await this.musicPlayer.previous();
      // 手动更新当前音乐信息
      this.updateCurrentMusicInfo();
      console.info('🎵 切换到:', this.currentMusic?.title || '未知');
      if (this.currentMusic) {
        await this.musicDataService.addToRecentPlay(this.currentMusic.id);
      }
    } catch (error) {
      console.error('上一首操作失败:', error);
    }
  }

  /**
   * 下一首
   */
  private async onNextClick(): Promise<void> {
    try {
      console.info('🎵 点击下一首');
      await this.musicPlayer.next();
      // 手动更新当前音乐信息
      this.updateCurrentMusicInfo();
      console.info('🎵 切换到:', this.currentMusic?.title || '未知');
      if (this.currentMusic) {
        await this.musicDataService.addToRecentPlay(this.currentMusic.id);
      }
    } catch (error) {
      console.error('下一首操作失败:', error);
    }
  }

  /**
   * 点赞/取消点赞
   */
  private async onLikeClick(): Promise<void> {
    if (!this.currentMusic) return;

    try {
      if (this.isLiked) {
        await this.musicDataService.unlikeSong(this.currentMusic.id);
        this.isLiked = false;
      } else {
        await this.musicDataService.likeSong(this.currentMusic.id);
        this.isLiked = true;
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
    }
  }

  /**
   * 收藏/取消收藏
   */
  private async onFavoriteClick(): Promise<void> {
    if (!this.currentMusic) return;

    try {
      if (this.isFavorite) {
        await this.musicDataService.unfavoriteSong(this.currentMusic.id);
        this.isFavorite = false;
      } else {
        await this.musicDataService.favoriteSong(this.currentMusic.id);
        this.isFavorite = true;
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
    }
  }

  /**
   * 播放模式切换
   */
  private async onPlayModeClick(): Promise<void> {
    const modes = [PlayMode.SEQUENCE, PlayMode.LOOP, PlayMode.SINGLE, PlayMode.RANDOM];
    const currentIndex = modes.indexOf(this.playMode);
    this.playMode = modes[(currentIndex + 1) % modes.length];

    this.musicPlayer.setPlayMode(this.playMode);
    await this.musicDataService.setPlayMode(this.playMode);
  }

  /**
   * 进度条拖拽
   */
  private async onSeekTo(position: number): Promise<void> {
    try {
      await this.musicPlayer.seekTo(position);
    } catch (error) {
      console.error('跳转播放位置失败:', error);
    }
  }

  /**
   * 音量控制
   */
  private async onVolumeChange(volume: number): Promise<void> {
    try {
      this.volume = Math.round(volume);
      await this.musicPlayer.setVolume(this.volume);
      // 保存音量设置到用户偏好
      await this.musicDataService.setVolume(this.volume);
      console.info(`音量调节为: ${this.volume}%`);
    } catch (error) {
      console.error('音量调节失败:', error);
    }
  }

  /**
   * 格式化时间显示
   */
  private formatTime(milliseconds: number): string {
    return MusicDataService.formatTime(milliseconds);
  }

  /**
   * 获取播放模式图标
   */
  private getPlayModeIcon(): string {
    switch (this.playMode) {
      case PlayMode.SEQUENCE:
        return 'app.media.huan';  // 使用现有的循环图标
      case PlayMode.LOOP:
        return 'app.media.huan';
      case PlayMode.SINGLE:
        return 'app.media.huan';
      case PlayMode.RANDOM:
        return 'app.media.huan';
      default:
        return 'app.media.huan';
    }
  }

  /**
   * 获取播放状态图标
   */
  private getPlayStateIcon(): string {
    switch (this.playState) {
      case PlayState.PLAYING:
        return 'app.media.bo';  // 播放中显示暂停图标（使用现有图标）
      case PlayState.PAUSED:
      case PlayState.STOPPED:
        return 'app.media.bo';  // 暂停/停止显示播放图标
      case PlayState.LOADING:
        return 'app.media.bo';  // 加载中也显示播放图标
      default:
        return 'app.media.bo';
    }
  }

  /**
   * 获取播放状态文本
   */
  private getPlayStateText(): string {
    switch (this.playState) {
      case PlayState.PLAYING:
        return '正在播放';
      case PlayState.PAUSED:
        return '已暂停';
      case PlayState.STOPPED:
        return '已停止';
      case PlayState.LOADING:
        return '加载中';
      case PlayState.ERROR:
        return '播放错误';
      default:
        return '未知状态';
    }
  }

  /**
   * 处理从推荐面板选择音乐
   */
  private onMusicSelectFromRecommendation(music: MusicInfo): void {
    console.info('🎯 从推荐中选择音乐:', music.title);

    // 查找音乐在播放列表中的索引
    const index = this.playlist.findIndex(item => item.id === music.id);
    if (index !== -1) {
      // 如果音乐在播放列表中，直接播放
      this.musicPlayer.playByIndex(index);
    } else {
      // 如果音乐不在播放列表中，添加到播放列表并播放
      this.playlist.push(music);
      this.musicPlayer.setPlaylist(this.playlist);
      this.musicPlayer.playByIndex(this.playlist.length - 1);
    }

    // 关闭推荐面板
    this.showRecommendations = false;
  }

  /**
   * 处理驾驶模式配置变化
   */
  private onDrivingModeConfigChange(config: DrivingModeConfig): void {
    console.info('🚗 驾驶模式配置变化:', config);
    this.drivingModeConfig = {
      isDrivingMode: config.isDrivingMode,
      simplifiedControls: config.simplifiedControls,
      voiceControlEnabled: config.voiceControlEnabled,
      largeButtonMode: config.largeButtonMode,
      autoVolumeAdjust: config.autoVolumeAdjust,
      speedBasedVolume: config.speedBasedVolume,
      safetyReminders: config.safetyReminders,
      nightMode: config.nightMode
    };

    // 如果启用了驾驶模式，应用相关设置
    if (config.isDrivingMode) {
      // 自动音量调节
      if (config.autoVolumeAdjust && this.volume < 60) {
        this.onVolumeChange(60); // 设置适合驾驶的音量
      }

      // 夜间模式处理（这里可以调整界面主题）
      if (config.nightMode) {
        console.info('🌙 启用夜间模式');
      }

      // 语音控制启用提示
      if (config.voiceControlEnabled) {
        console.info('🎤 语音控制已启用');
      }
    }
  }

  /**
   * 测试音频格式
   */
  private async testAudioFormats(): Promise<void> {
    console.info('🧪 开始测试音频格式...');
    try {
      await this.musicPlayer.testAllAudioFormats();
      console.info('🧪 音频格式测试完成，请查看控制台日志');
    } catch (error) {
      console.error('🧪 音频格式测试失败:', error);
    }
  }

  /**
   * 获取音量级别描述
   */
  private getVolumeLevel(): string {
    if (this.volume === 0) {
      return '静音';
    } else if (this.volume <= 20) {
      return '很小声';
    } else if (this.volume <= 40) {
      return '小声';
    } else if (this.volume <= 60) {
      return '适中';
    } else if (this.volume <= 80) {
      return '大声';
    } else {
      return '很大声';
    }
  }

  /**
   * 获取播放模式文本
   */
  private getPlayModeText(): string {
    switch (this.playMode) {
      case PlayMode.SEQUENCE:
        return '顺序';
      case PlayMode.LOOP:
        return '循环';
      case PlayMode.SINGLE:
        return '单曲';
      case PlayMode.RANDOM:
        return '随机';
      default:
        return '顺序';
    }
  }
}
