@echo off
chcp 65001 >nul
echo 开始编译测试...
echo.

cd /d "%~dp0"

echo 当前目录: %CD%
echo.

echo 检查hvigor文件...
if exist hvigorw.bat (
    echo 找到 hvigorw.bat
    echo 执行清理...
    call hvigorw.bat clean
    echo.
    echo 执行编译...
    call hvigorw.bat assembleHap
) else if exist hvigorw (
    echo 找到 hvigorw
    echo 执行清理...
    call hvigorw clean
    echo.
    echo 执行编译...
    call hvigorw assembleHap
) else (
    echo 未找到hvigor构建脚本
    echo 请在DevEco Studio中打开项目进行编译
)

echo.
echo 编译测试完成
pause
