@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 📱 HarmonyOS模拟器部署脚本
echo ========================================
echo.

:: 检查当前目录
if not exist "entry" (
    echo ❌ 错误：请在项目根目录运行此脚本
    echo 当前目录：%CD%
    echo 请确保在包含 entry 文件夹的目录中运行
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过
echo 📁 项目路径：%CD%
echo.

:: 检查必要的命令
echo 🔍 检查开发环境...

where ohpm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到 ohpm 命令
    echo 请确保已安装 DevEco Studio 并配置环境变量
    goto :show_help
)
echo ✅ ohpm 命令可用

where hdc >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到 hdc 命令
    echo 请确保 HarmonyOS SDK 工具链已正确安装
    goto :show_help
)
echo ✅ hdc 命令可用

if not exist "hvigorw.bat" (
    echo ❌ 错误：未找到 hvigorw.bat
    echo 请确保在正确的 HarmonyOS 项目目录中
    pause
    exit /b 1
)
echo ✅ 构建工具可用
echo.

:: 检查模拟器连接
echo 🔍 检查模拟器连接状态...
hdc list targets > temp_devices.txt 2>&1

set "EMULATOR_FOUND=false"
set "EMULATOR_ID="

for /f "tokens=*" %%i in (temp_devices.txt) do (
    set "line=%%i"
    echo !line! | findstr /i "emulator" >nul
    if !errorlevel! equ 0 (
        set "EMULATOR_FOUND=true"
        set "EMULATOR_ID=%%i"
    )
)

del temp_devices.txt >nul 2>&1

if "%EMULATOR_FOUND%"=="false" (
    echo ⚠️ 警告：未检测到运行中的模拟器
    echo.
    echo 💡 请按照以下步骤启动模拟器：
    echo 1. 打开 DevEco Studio
    echo 2. 点击 Tools → Device Manager
    echo 3. 创建新模拟器（如果没有）：
    echo    - 设备类型：Phone
    echo    - 系统版本：HarmonyOS 3.0+ (API 9+)
    echo    - 内存：4GB (推荐)
    echo 4. 启动模拟器并等待完全启动
    echo 5. 重新运行此脚本
    echo.
    echo 📚 详细步骤请参考：📱模拟器部署指南.md
    pause
    exit /b 1
)

echo ✅ 检测到模拟器：%EMULATOR_ID%
echo.

:: 安装依赖
echo 📦 正在安装项目依赖...
ohpm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 清理缓存：ohpm cache clean
    echo 3. 删除 oh_modules 文件夹后重试
    pause
    exit /b 1
)
echo ✅ 依赖安装完成
echo.

:: 清理项目
echo 🧹 正在清理项目...
call hvigorw.bat clean
if %errorlevel% neq 0 (
    echo ⚠️ 警告：项目清理失败，继续构建...
)
echo ✅ 项目清理完成
echo.

:: 构建项目
echo 🔨 正在构建项目...
call hvigorw.bat assembleHap
if %errorlevel% neq 0 (
    echo ❌ 项目构建失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查代码语法错误
    echo 2. 确认 SDK 版本兼容性
    echo 3. 在 DevEco Studio 中查看详细错误信息
    echo 4. 检查 entry/src/main/ets 目录下的文件
    pause
    exit /b 1
)
echo ✅ 项目构建成功
echo.

:: 检查构建产物
set "HAP_FILE=entry\build\default\outputs\default\entry-default-signed.hap"
if not exist "%HAP_FILE%" (
    echo ❌ 错误：未找到构建产物
    echo 预期位置：%HAP_FILE%
    echo.
    echo 💡 请检查构建过程是否有错误
    pause
    exit /b 1
)

echo ✅ 构建产物检查通过
echo 📦 HAP文件：%HAP_FILE%
echo 📏 文件大小：
for %%A in ("%HAP_FILE%") do echo    %%~zA 字节
echo.

:: 卸载旧版本应用（如果存在）
echo 🗑️ 清理旧版本应用...
hdc uninstall com.example.zuoye1 >nul 2>&1
echo ✅ 清理完成
echo.

:: 安装应用到模拟器
echo 📱 正在安装应用到模拟器...
echo 目标设备：%EMULATOR_ID%
hdc install "%HAP_FILE%"
if %errorlevel% neq 0 (
    echo ❌ 应用安装失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查模拟器是否正常运行
    echo 2. 重启模拟器
    echo 3. 重启 hdc 服务：
    echo    hdc kill-server
    echo    hdc start-server
    echo 4. 检查模拟器存储空间
    pause
    exit /b 1
)
echo ✅ 应用安装成功
echo.

:: 启动应用
echo 🚀 正在启动应用...
hdc shell aa start -a EntryAbility -b com.example.zuoye1
if %errorlevel% neq 0 (
    echo ⚠️ 警告：自动启动失败
    echo 💡 请在模拟器中手动启动应用：
    echo 1. 在模拟器桌面找到 "zuoye1" 应用图标
    echo 2. 点击图标启动应用
) else (
    echo ✅ 应用启动成功
)
echo.

:: 显示成功信息
echo ========================================
echo 🎉 模拟器部署完成！
echo ========================================
echo.
echo 📱 应用已成功部署到模拟器：%EMULATOR_ID%
echo 🎵 您现在可以在模拟器中使用车载娱乐系统了！
echo.
echo 🎯 主要功能验证：
echo    ✅ 检查应用图标是否出现在模拟器桌面
echo    ✅ 点击图标启动应用
echo    ✅ 验证音乐播放界面显示正常
echo    ✅ 测试播放控制按钮功能
echo    ✅ 检查网络封面图片加载
echo    ✅ 测试点赞收藏等交互功能
echo.
echo 🌐 网络音乐功能：
echo    - 封面图片来源：酷狗音乐 URL
echo    - 支持在线音频流播放
echo    - 自动网络状态检测
echo    - 加载失败时显示默认图片
echo.
echo 🔧 如果遇到问题：
echo    1. 检查模拟器网络连接
echo    2. 确认应用权限设置
echo    3. 查看 DevEco Studio 日志输出
echo    4. 参考 📱模拟器部署指南.md
echo.
echo 📚 相关文档：
echo    - 📱模拟器部署指南.md
echo    - 网络音乐功能使用说明.md
echo    - 🎵网络音乐功能实现完成报告.md
echo.
goto :end

:show_help
echo.
echo 💡 环境配置帮助：
echo.
echo 1. 安装 DevEco Studio：
echo    - 下载：https://developer.harmonyos.com/cn/develop/deveco-studio
echo    - 版本：4.0 或更高
echo.
echo 2. 配置 HarmonyOS SDK：
echo    - 在 DevEco Studio 中配置 SDK
echo    - 确保 API 版本 9 或更高
echo    - 下载模拟器镜像
echo.
echo 3. 配置环境变量：
echo    - 将 SDK 的 toolchains 目录添加到 PATH
echo    - 例如：C:\Users\<USER>\AppData\Local\Huawei\Sdk\toolchains
echo.
echo 4. 创建和启动模拟器：
echo    - DevEco Studio → Tools → Device Manager
echo    - 创建新模拟器（Phone, API 9+）
echo    - 启动模拟器并等待完全启动
echo.
pause
exit /b 1

:end
pause
