// 每日推荐页面
import router from '@ohos.router';

// 定义音乐信息接口
interface MusicItem {
  title: string;
  artist: string;
  duration: string;
}

@Entry
@Component
struct RecommendPage {
  @State recommendList: MusicItem[] = [
    { title: '只想守护你', artist: '张杰', duration: '04:32' },
    { title: '逆战', artist: '张杰', duration: '03:45' },
    { title: '他不懂', artist: '张杰', duration: '04:18' },
    { title: '天下', artist: '张杰', duration: '04:02' },
    { title: '少年中国说', artist: '张杰', duration: '03:28' },
    { title: '我们都一样', artist: '张杰', duration: '04:15' },
    { title: '明天过后', artist: '张杰', duration: '04:08' },
    { title: '这就是爱', artist: '张杰', duration: '03:52' }
  ]

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor('#FFFFFF')
          .onClick(() => {
            router.back();
          })

        Text('每日推荐')
          .fontSize(20)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('🔍')
          .fontSize(20)
          .fontColor('#FFFFFF')
      }
      .width('100%')
      .height(60)
      .padding({ left: 20, right: 20 })
      .backgroundColor('#FF5722')
      .alignItems(VerticalAlign.Center)

      // 推荐音乐列表
      List() {
        ForEach(this.recommendList, (item: MusicItem, index: number) => {
          ListItem() {
            Row() {
              // 音乐封面
              Image($r('app.media.yinyue'))
                .width(50)
                .height(50)
                .borderRadius(8)
                .margin({ right: 15 })

              // 音乐信息
              Column() {
                Text(item.title)
                  .fontSize(16)
                  .fontColor('#FFFFFF')
                  .fontWeight(FontWeight.Medium)
                  .maxLines(1)

                Text(item.artist)
                  .fontSize(14)
                  .fontColor('#CCCCCC')
                  .margin({ top: 4 })
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1)

              // 时长和播放按钮
              Row() {
                Text(item.duration)
                  .fontSize(12)
                  .fontColor('#CCCCCC')
                  .margin({ right: 15 })

                Text('▶️')
                  .fontSize(16)
                  .onClick(() => {
                    console.info(`播放: ${item.title}`);
                  })
              }
            }
            .width('100%')
            .padding(15)
            .backgroundColor(index % 2 === 0 ? 'rgba(40, 40, 40, 0.5)' : 'transparent')
            .borderRadius(8)
            .onClick(() => {
              console.info(`选择音乐: ${item.title}`);
            })
          }
          .margin({ bottom: 8 })
        })
      }
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#1A1A1A')

      // 底部播放控制栏
      Row() {
        Image($r('app.media.yinyue'))
          .width(40)
          .height(40)
          .borderRadius(6)
          .margin({ right: 12 })

        Column() {
          Text('只想守护你')
            .fontSize(14)
            .fontColor('#FFFFFF')
            .maxLines(1)

          Text('张杰')
            .fontSize(12)
            .fontColor('#CCCCCC')
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Row() {
          Text('⏮️')
            .fontSize(20)
            .fontColor('#FFFFFF')
            .margin({ right: 15 })

          Text('⏸️')
            .fontSize(24)
            .fontColor('#00D4FF')
            .margin({ right: 15 })

          Text('⏭️')
            .fontSize(20)
            .fontColor('#FFFFFF')
        }
      }
      .width('100%')
      .height(70)
      .padding({ left: 15, right: 15 })
      .backgroundColor('#2A2A2A')
      .alignItems(VerticalAlign.Center)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
  }
}
