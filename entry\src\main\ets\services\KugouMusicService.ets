/**
 * 酷狗音乐API服务
 * 用于获取在线音乐资源和封面图片
 */

import { http } from '@kit.NetworkKit';
import { MusicInfo } from '../common/MusicTypes';

/**
 * 酷狗音乐搜索结果接口
 */
interface KugouSearchData {
  lists: Array<KugouSongInfo>;
}

interface KugouSongInfo {
  FileHash: string;
  SongName: string;
  SingerName: string;
  AlbumName: string;
  Duration: number;
  Bitrate: number;
}

interface KugouSearchResult {
  status: number;
  data: KugouSearchData;
}

/**
 * 酷狗音乐播放URL结果接口
 */
interface KugouPlayUrlData {
  play_url: string;
  img: string;
  author_name: string;
  song_name: string;
  album_name: string;
}

interface KugouPlayUrlResult {
  status: number;
  data: KugouPlayUrlData;
}

interface PlayUrlResult {
  playUrl: string;
  coverUrl: string;
}

export class KugouMusicService {
  private static readonly BASE_URL = 'http://mobilecdn.kugou.com';
  private static readonly SEARCH_URL = '/api/v3/search/song';
  private static readonly PLAY_URL_API = '/api/v3/song/play_url';

  /**
   * 搜索音乐
   * @param keyword 搜索关键词
   * @param page 页码，默认1
   * @param pageSize 每页数量，默认20
   */
  public static async searchMusic(keyword: string, page: number = 1, pageSize: number = 20): Promise<MusicInfo[]> {
    try {
      const searchParams = `format=json&keyword=${encodeURIComponent(keyword)}&page=${page}&pagesize=${pageSize}&showtype=1`;

      const response = await http.createHttp().request(
        `${KugouMusicService.BASE_URL}${KugouMusicService.SEARCH_URL}?${searchParams}`,
        {
          method: http.RequestMethod.GET,
          header: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://www.kugou.com/'
          }
        }
      );

      if (response.responseCode === 200) {
        const result: KugouSearchResult = JSON.parse(response.result as string);
        if (result.status === 1 && result.data && result.data.lists) {
          return result.data.lists.map((item: KugouSongInfo, index: number): MusicInfo => {
            return {
              id: item.FileHash || `kugou_${Date.now()}_${index}`,
              title: item.SongName || '未知歌曲',
              artist: item.SingerName || '未知艺术家',
              album: item.AlbumName || '未知专辑',
              duration: (item.Duration || 0) * 1000, // 转换为毫秒
              uri: '', // 需要通过getPlayUrl获取
              coverUri: '', // 需要通过getPlayUrl获取
              isLiked: false,
              isFavorite: false,
              playCount: 0,
              isNetworkResource: true
            };
          });
        }
      }
      
      console.error('搜索音乐失败:', response.responseCode, response.result);
      return [];
    } catch (error) {
      console.error('搜索音乐异常:', error);
      return [];
    }
  }

  /**
   * 获取音乐播放URL和封面
   * @param fileHash 音乐文件哈希值
   */
  public static async getPlayUrl(fileHash: string): Promise<PlayUrlResult | null> {
    try {
      const params = `cmd=playInfo&hash=${encodeURIComponent(fileHash)}`;

      const response = await http.createHttp().request(
        `${KugouMusicService.BASE_URL}${KugouMusicService.PLAY_URL_API}?${params}`,
        {
          method: http.RequestMethod.GET,
          header: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://www.kugou.com/'
          }
        }
      );

      if (response.responseCode === 200) {
        const result: KugouPlayUrlResult = JSON.parse(response.result as string);
        if (result.status === 1 && result.data) {
          const playUrlResult: PlayUrlResult = {
            playUrl: result.data.play_url || '',
            coverUrl: result.data.img || ''
          };
          return playUrlResult;
        }
      }
      
      console.error('获取播放URL失败:', response.responseCode, response.result);
      return null;
    } catch (error) {
      console.error('获取播放URL异常:', error);
      return null;
    }
  }

  /**
   * 获取热门音乐推荐
   */
  public static async getHotMusic(): Promise<MusicInfo[]> {
    const hotKeywords = ['流行', '经典', '热门', '新歌', '华语'];
    const randomKeyword = hotKeywords[Math.floor(Math.random() * hotKeywords.length)];
    return await KugouMusicService.searchMusic(randomKeyword, 1, 10);
  }

  /**
   * 根据艺术家获取音乐
   * @param artist 艺术家名称
   */
  public static async getMusicByArtist(artist: string): Promise<MusicInfo[]> {
    return await KugouMusicService.searchMusic(artist, 1, 20);
  }

  /**
   * 获取完整的音乐信息（包含播放URL和封面）
   * @param musicInfo 基础音乐信息
   */
  public static async getCompleteMusic(musicInfo: MusicInfo): Promise<MusicInfo> {
    try {
      const urlInfo = await KugouMusicService.getPlayUrl(musicInfo.id);
      if (urlInfo) {
        return {
          id: musicInfo.id,
          title: musicInfo.title,
          artist: musicInfo.artist,
          album: musicInfo.album,
          duration: musicInfo.duration,
          uri: urlInfo.playUrl,
          coverUri: urlInfo.coverUrl,
          isLiked: musicInfo.isLiked,
          isFavorite: musicInfo.isFavorite,
          playCount: musicInfo.playCount,
          isNetworkResource: musicInfo.isNetworkResource
        };
      }
      return musicInfo;
    } catch (error) {
      console.error('获取完整音乐信息失败:', error);
      return musicInfo;
    }
  }

  /**
   * 批量获取完整音乐信息
   * @param musicList 音乐列表
   */
  public static async getCompleteMusicList(musicList: MusicInfo[]): Promise<MusicInfo[]> {
    const promises = musicList.map((music: MusicInfo) => KugouMusicService.getCompleteMusic(music));
    try {
      return await Promise.all(promises);
    } catch (error) {
      console.error('批量获取音乐信息失败:', error);
      return musicList;
    }
  }

  /**
   * 验证网络连接
   */
  public static async checkNetworkConnection(): Promise<boolean> {
    try {
      const response = await http.createHttp().request(
        'https://www.baidu.com',
        {
          method: http.RequestMethod.GET,
          connectTimeout: 5000,
          readTimeout: 5000
        }
      );
      return response.responseCode === 200;
    } catch (error) {
      console.error('网络连接检查失败:', error);
      return false;
    }
  }

  /**
   * 获取音乐封面图片URL（酷狗格式）
   * @param size 图片尺寸，默认400
   */
  public static getKugouCoverUrl(hash: string, size: number = 400): string {
    return `https://imgessl.kugou.com/stdmusic/${size}/${hash}.jpg`;
  }

  /**
   * 生成示例本地音乐列表（使用项目中的图片资源作为封面）
   *
   * 🎵 如何添加真实音乐文件：
   * 1. 将MP3文件放入 entry/src/main/resources/rawfile/music/ 文件夹
   * 2. 将封面图片放入 entry/src/main/resources/base/media/ 文件夹
   * 3. 修改下面的配置，更新文件路径和信息
   *
   * 📁 推荐的文件夹结构：
   * rawfile/music/
   * ├── song1.mp3 (夜空中最亮的星)
   * ├── song2.mp3 (成都)
   * ├── song3.mp3 (告白气球)
   * ├── song4.mp3 (演员)
   * └── song5.mp3 (稻香)
   *
   * base/media/
   * ├── cover1.jpg (对应song1的封面)
   * ├── cover2.jpg (对应song2的封面)
   * ├── cover3.jpg (对应song3的封面)
   * ├── cover4.jpg (对应song4的封面)
   * └── cover5.jpg (对应song5的封面)
   */
  public static getExampleNetworkMusicList(): MusicInfo[] {
    return [
      {
        id: 'local_001',
        title: '夜空中最亮的星',
        artist: '逃跑计划',
        album: '世界',
        duration: 240000, // 4分钟
        uri: 'rawfile://music/song1.mp3', // 🎵 使用本地音频文件
        coverUri: 'https://p2.music.126.net/RLeBJe4D_qONxoaQDdyOdg==/109951163071286395.jpg', // 🖼️ 高质量网络封面图片
        isLiked: false,
        isFavorite: false,
        playCount: 0,
        isNetworkResource: true // 使用网络封面图片
      },
      {
        id: 'local_002',
        title: '成都',
        artist: '赵雷',
        album: '无法长大',
        duration: 260000, // 4分20秒
        uri: 'rawfile://music/song2.mp3', // 🎵 使用本地音频文件
        coverUri: 'https://p2.music.126.net/34YW1QtKxJ_3YnX9ZzKhzw==/2946691234868155.jpg', // 🖼️ 高质量网络封面图片
        isLiked: false,
        isFavorite: false,
        playCount: 0,
        isNetworkResource: true // 使用网络封面图片
      },
      {
        id: 'local_003',
        title: '告白气球',
        artist: '周杰伦',
        album: '周杰伦的床边故事',
        duration: 327000, // 5分27秒
        uri: 'rawfile://music/song3.mp3', // 🎵 使用本地音频文件
        coverUri: 'https://p2.music.126.net/gx8g4AaYG8HynHvnPhOnRA==/18590542604286213.jpg', // 🖼️ 高质量网络封面图片
        isLiked: false,
        isFavorite: false,
        playCount: 0,
        isNetworkResource: true // 使用网络封面图片
      },
      {
        id: 'local_004',
        title: '演员',
        artist: '薛之谦',
        album: '绅士',
        duration: 285000, // 4分45秒
        uri: 'rawfile://music/song4.mp3', // 🎵 使用本地音频文件
        coverUri: 'https://p2.music.126.net/BoMr8Kk8UG8GtWdYWulftg==/109951165500327915.jpg', // 🖼️ 高质量网络封面图片
        isLiked: false,
        isFavorite: false,
        playCount: 0,
        isNetworkResource: true // 使用网络封面图片
      },
      {
        id: 'local_005',
        title: '稻香',
        artist: '周杰伦',
        album: '魔杰座',
        duration: 312000, // 5分12秒
        uri: 'rawfile://music/song5.mp3', // 🎵 使用本地音频文件
        coverUri: 'https://p2.music.126.net/hzDXu_D2QJObeVEhOjzQJQ==/2946691248081599.jpg', // 🖼️ 高质量网络封面图片
        isLiked: false,
        isFavorite: false,
        playCount: 0,
        isNetworkResource: true // 使用网络封面图片
      }
    ];
  }
}
