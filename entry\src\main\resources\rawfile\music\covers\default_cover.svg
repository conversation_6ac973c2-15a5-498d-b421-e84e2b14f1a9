<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="noteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="300" height="300" fill="url(#bgGradient)" rx="20"/>
  
  <!-- 音符图标 -->
  <g transform="translate(150,150)">
    <!-- 主音符 -->
    <ellipse cx="-20" cy="30" rx="15" ry="10" fill="url(#noteGradient)" transform="rotate(-20)"/>
    <rect x="-8" y="-40" width="4" height="70" fill="url(#noteGradient)"/>
    
    <!-- 副音符 -->
    <ellipse cx="20" cy="20" rx="12" ry="8" fill="url(#noteGradient)" transform="rotate(-20)"/>
    <rect x="28" y="-30" width="3" height="50" fill="url(#noteGradient)"/>
    
    <!-- 连接线 -->
    <path d="M -4,-40 Q 10,-45 31,-30" stroke="url(#noteGradient)" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 装饰圆圈 -->
  <circle cx="80" cy="80" r="3" fill="rgba(255,255,255,0.3)"/>
  <circle cx="220" cy="100" r="2" fill="rgba(255,255,255,0.4)"/>
  <circle cx="250" cy="200" r="4" fill="rgba(255,255,255,0.2)"/>
  <circle cx="60" cy="220" r="2.5" fill="rgba(255,255,255,0.3)"/>
</svg>
