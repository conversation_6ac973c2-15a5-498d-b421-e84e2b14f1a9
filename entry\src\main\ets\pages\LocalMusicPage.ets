// 本地音乐页面
import router from '@ohos.router';

// 定义本地音乐信息接口
interface LocalMusicItem {
  title: string;
  artist: string;
  duration: string;
  size: string;
}

@Entry
@Component
struct LocalMusicPage {
  @State localMusicList: LocalMusicItem[] = [
    { title: '只想守护你', artist: '张杰', duration: '04:32', size: '6.8MB' },
    { title: '逆战', artist: '张杰', duration: '03:45', size: '5.2MB' },
    { title: '他不懂', artist: '张杰', duration: '04:18', size: '6.1MB' },
    { title: '天下', artist: '张杰', duration: '04:02', size: '5.8MB' },
    { title: '少年中国说', artist: '张杰', duration: '03:28', size: '4.9MB' }
  ]

  @State selectedCount: number = 0
  @State isSelectMode: boolean = false

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor('#FFFFFF')
          .onClick(() => {
            router.back();
          })

        Text(this.isSelectMode ? `已选择 ${this.selectedCount} 首` : '本地音乐')
          .fontSize(20)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text(this.isSelectMode ? '取消' : '选择')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .onClick(() => {
            this.isSelectMode = !this.isSelectMode;
            if (!this.isSelectMode) {
              this.selectedCount = 0;
            }
          })
      }
      .width('100%')
      .height(60)
      .padding({ left: 20, right: 20 })
      .backgroundColor('#3F51B5')
      .alignItems(VerticalAlign.Center)

      // 统计信息
      Row() {
        Column() {
          Text(`${this.localMusicList.length}`)
            .fontSize(24)
            .fontColor('#3F51B5')
            .fontWeight(FontWeight.Bold)

          Text('首歌曲')
            .fontSize(14)
            .fontColor('#CCCCCC')
        }
        .margin({ right: 30 })

        Column() {
          Text('32.8MB')
            .fontSize(24)
            .fontColor('#3F51B5')
            .fontWeight(FontWeight.Bold)

          Text('总大小')
            .fontSize(14)
            .fontColor('#CCCCCC')
        }

        Blank()

        Text('🔄')
          .fontSize(20)
          .fontColor('#3F51B5')
          .onClick(() => {
            console.info('刷新本地音乐');
          })
      }
      .width('100%')
      .padding(20)
      .backgroundColor('rgba(63, 81, 181, 0.1)')

      // 本地音乐列表
      List() {
        ForEach(this.localMusicList, (item: LocalMusicItem, index: number) => {
          ListItem() {
            Row() {
              // 选择框（选择模式下显示）
              if (this.isSelectMode) {
                Text('☑️')
                  .fontSize(20)
                  .margin({ right: 15 })
                  .onClick(() => {
                    // 这里应该处理选择逻辑
                    console.info(`选择/取消选择: ${item.title}`);
                  })
              }

              // 音乐封面
              Image($r('app.media.yinyue'))
                .width(50)
                .height(50)
                .borderRadius(8)
                .margin({ right: 15 })

              // 音乐信息
              Column() {
                Text(item.title)
                  .fontSize(16)
                  .fontColor('#FFFFFF')
                  .fontWeight(FontWeight.Medium)
                  .maxLines(1)

                Row() {
                  Text(item.artist)
                    .fontSize(14)
                    .fontColor('#CCCCCC')

                  Text('•')
                    .fontSize(14)
                    .fontColor('#CCCCCC')
                    .margin({ left: 8, right: 8 })

                  Text(item.size)
                    .fontSize(14)
                    .fontColor('#CCCCCC')
                }
                .margin({ top: 4 })
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1)

              // 时长和更多按钮
              Column() {
                Text(item.duration)
                  .fontSize(12)
                  .fontColor('#CCCCCC')

                Text('⋮')
                  .fontSize(16)
                  .fontColor('#CCCCCC')
                  .margin({ top: 4 })
                  .onClick(() => {
                    console.info(`更多操作: ${item.title}`);
                  })
              }
              .alignItems(HorizontalAlign.End)
            }
            .width('100%')
            .padding(15)
            .backgroundColor(index % 2 === 0 ? 'rgba(40, 40, 40, 0.3)' : 'transparent')
            .borderRadius(8)
            .onClick(() => {
              if (!this.isSelectMode) {
                console.info(`播放: ${item.title}`);
              }
            })
          }
          .margin({ bottom: 8 })
        })
      }
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#1A1A1A')

      // 底部操作栏（选择模式下显示）
      if (this.isSelectMode && this.selectedCount > 0) {
        Row() {
          Text('🗑️')
            .fontSize(20)
            .fontColor('#FF6B6B')
            .onClick(() => {
              console.info('删除选中的音乐');
            })

          Blank()

          Text('📤')
            .fontSize(20)
            .fontColor('#4CAF50')
            .onClick(() => {
              console.info('分享选中的音乐');
            })

          Blank()

          Text('➕')
            .fontSize(20)
            .fontColor('#FF9800')
            .onClick(() => {
              console.info('添加到歌单');
            })
        }
        .width('100%')
        .height(60)
        .padding({ left: 30, right: 30 })
        .backgroundColor('#2A2A2A')
        .alignItems(VerticalAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
  }
}
