// 全新极简音乐播放页面
import router from '@ohos.router';

@Entry
@Component
struct MusicPage {
  @State currentMusic: string = '只想守护你'
  @State currentArtist: string = '张杰'
  @State isPlaying: boolean = false
  @State currentTime: string = '02:15'
  @State totalTime: string = '04:32'
  @State progress: number = 35

  build() {
    Column() {
      // 顶部状态栏
      Row() {
        Text('CarMusic')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)

        Blank()

        Text('30°C')
          .fontSize(14)
          .fontColor('#FFFFFF')
          .backgroundColor('#444444')
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .borderRadius(8)
      }
      .width('100%')
      .height(40)
      .padding({ left: 15, right: 15 })
      .backgroundColor('#000000')
      .alignItems(VerticalAlign.Center)

      // 主内容区域 - 全新设计
      Scroll() {
        Column() {
          // 主播放区域 - 更大更突出
          Column() {
            Row() {
              // 更大的封面
              Image($r('app.media.yinyue'))
                .width(100)
                .height(100)
                .borderRadius(16)

              // 歌曲信息
              Column() {
                Text(this.currentMusic)
                  .fontSize(20)
                  .fontColor('#FFFFFF')
                  .fontWeight(FontWeight.Bold)
                  .maxLines(1)

                Text(this.currentArtist)
                  .fontSize(16)
                  .fontColor('#AAAAAA')
                  .margin({ top: 6 })

                // 进度条区域
                Column() {
                  Progress({
                    value: this.progress,
                    total: 100,
                    type: ProgressType.Linear
                  })
                  .width('100%')
                  .height(4)
                  .color('#00D4FF')
                  .backgroundColor('#444444')
                  .margin({ top: 12, bottom: 8 })

                  Row() {
                    Text(this.currentTime)
                      .fontSize(12)
                      .fontColor('#AAAAAA')

                    Blank()

                    Text(this.totalTime)
                      .fontSize(12)
                      .fontColor('#AAAAAA')
                  }
                  .width('100%')
                }
                .width('100%')
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1)
              .margin({ left: 20 })

              // 播放按钮
              Text(this.isPlaying ? '⏸️' : '▶️')
                .fontSize(28)
                .fontColor('#FFFFFF')
                .backgroundColor('#00D4FF')
                .width(60)
                .height(60)
                .borderRadius(30)
                .textAlign(TextAlign.Center)
                .onClick(() => {
                  this.isPlaying = !this.isPlaying;
                  console.info(this.isPlaying ? '播放' : '暂停');
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)
          }
          .width('100%')
          .padding(25)
          .backgroundColor('#222222')
          .borderRadius(20)
          .margin({ bottom: 25 })

          // 播放控制按钮
          Row() {
            Text('⏮️')
              .fontSize(24)
              .fontColor('#FFFFFF')
              .backgroundColor('#333333')
              .width(50)
              .height(50)
              .borderRadius(25)
              .textAlign(TextAlign.Center)
              .onClick(() => {
                console.info('上一首');
              })

            Text('❤️')
              .fontSize(24)
              .fontColor('#FF6B6B')
              .backgroundColor('#333333')
              .width(50)
              .height(50)
              .borderRadius(25)
              .textAlign(TextAlign.Center)
              .onClick(() => {
                console.info('收藏');
              })

            Text('⏭️')
              .fontSize(24)
              .fontColor('#FFFFFF')
              .backgroundColor('#333333')
              .width(50)
              .height(50)
              .borderRadius(25)
              .textAlign(TextAlign.Center)
              .onClick(() => {
                console.info('下一首');
              })
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceEvenly)
          .margin({ bottom: 30 })

          // 简化功能菜单 - 只保留核心功能
          Column() {
            Text('更多功能')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 15 })

            // 单行功能按钮
            Row() {
              this.FunctionButton('💚', '我的音乐', () => {
                router.pushUrl({ url: 'pages/FavoritePage' });
              })

              this.FunctionButton('🎵', '歌单', () => {
                router.pushUrl({ url: 'pages/PlaylistPage' });
              })

              this.FunctionButton('🔍', '搜索', () => {
                router.pushUrl({ url: 'pages/SearchPage' });
              })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceEvenly)
          }
          .width('100%')
        }
        .padding(15)
      }
      .layoutWeight(1)
      .backgroundColor('#111111')

      // 底部导航
      Row() {
        Column() {
          Text('⚙️')
            .fontSize(18)
            .fontColor('#AAAAAA')
          Text('设置')
            .fontSize(10)
            .fontColor('#AAAAAA')
            .margin({ top: 2 })
        }
        .onClick(() => {
          console.info('设置');
        })

        Column() {
          Text('📱')
            .fontSize(18)
            .fontColor('#AAAAAA')
          Text('应用')
            .fontSize(10)
            .fontColor('#AAAAAA')
            .margin({ top: 2 })
        }
        .onClick(() => {
          console.info('应用');
        })

        Column() {
          Text('🎵')
            .fontSize(20)
            .fontColor('#00D4FF')
          Text('音乐')
            .fontSize(10)
            .fontColor('#00D4FF')
            .margin({ top: 2 })
        }

        Column() {
          Text('📊')
            .fontSize(18)
            .fontColor('#AAAAAA')
          Text('统计')
            .fontSize(10)
            .fontColor('#AAAAAA')
            .margin({ top: 2 })
        }
        .onClick(() => {
          console.info('统计');
        })
      }
      .width('100%')
      .height(55)
      .backgroundColor('#000000')
      .justifyContent(FlexAlign.SpaceEvenly)
      .alignItems(VerticalAlign.Center)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#111111')
  }


  // 功能按钮组件
  @Builder FunctionButton(icon: string, title: string, onClick: () => void) {
    Column() {
      Text(icon)
        .fontSize(20)
        .margin({ bottom: 4 })

      Text(title)
        .fontSize(11)
        .fontColor('#FFFFFF')
        .textAlign(TextAlign.Center)
    }
    .width(100)
    .height(60)
    .backgroundColor('#222222')
    .borderRadius(12)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(onClick)
  }
}
