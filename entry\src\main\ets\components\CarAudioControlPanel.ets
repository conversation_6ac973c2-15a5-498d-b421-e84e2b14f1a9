/**
 * 车载音频控制面板组件
 * 提供专业的音频控制界面
 */

import { CarAudioService, EqualizerPreset, SoundFieldMode, CarAudioConfig } from '../services/CarAudioService';

@Component
export struct CarAudioControlPanel {
  @State private carAudioService: CarAudioService = CarAudioService.getInstance();
  @State private audioConfig: CarAudioConfig = this.carAudioService.getConfig();
  @State private showAdvancedControls: boolean = false;

  aboutToAppear() {
    // 监听音频配置变化
    this.carAudioService.addConfigListener((config: CarAudioConfig) => {
      this.audioConfig = config;
    });
  }

  build() {
    Column() {
      // 主要音频控制区域
      this.buildMainControls()
      
      // 高级控制开关
      Row() {
        Text('高级音频控制')
          .fontColor('#FFFFFF')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
        
        Blank()
        
        Toggle({ type: ToggleType.Switch, isOn: this.showAdvancedControls })
          .selectedColor('#00D4FF')
          .switchPointColor('#FFFFFF')
          .onChange((isOn: boolean) => {
            this.showAdvancedControls = isOn;
          })
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 12, bottom: 12 })
      .backgroundColor('rgba(255, 255, 255, 0.05)')
      .borderRadius(12)
      .margin({ top: 15 })

      // 高级控制面板
      if (this.showAdvancedControls) {
        this.buildAdvancedControls()
      }
    }
    .width('100%')
  }

  @Builder
  buildMainControls() {
    Column() {
      // 均衡器预设选择
      Column() {
        Text('🎚️ 音效预设')
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 12 })
        
        Grid() {
          ForEach([
            EqualizerPreset.NORMAL,
            EqualizerPreset.ROCK,
            EqualizerPreset.POP,
            EqualizerPreset.JAZZ,
            EqualizerPreset.CLASSICAL,
            EqualizerPreset.VOCAL
          ], (preset: EqualizerPreset) => {
            GridItem() {
              Text(this.carAudioService.getEqualizerPresetName(preset))
                .fontColor(this.audioConfig.equalizerPreset === preset ? '#000000' : '#FFFFFF')
                .fontSize(14)
                .fontWeight(FontWeight.Medium)
                .textAlign(TextAlign.Center)
                .width('100%')
                .height(35)
                .backgroundColor(this.audioConfig.equalizerPreset === preset ? '#00D4FF' : 'rgba(255, 255, 255, 0.1)')
                .borderRadius(8)
                .onClick(() => {
                  this.carAudioService.setEqualizerPreset(preset);
                })
            }
          })
        }
        .columnsTemplate('1fr 1fr 1fr')
        .rowsTemplate('1fr 1fr')
        .columnsGap(8)
        .rowsGap(8)
        .width('100%')
        .height(80)
      }
      .width('100%')
      .padding(15)
      .backgroundColor('rgba(0, 212, 255, 0.08)')
      .borderRadius(15)
      .margin({ bottom: 15 })

      // 声场模式选择
      Column() {
        Text('🎭 声场模式')
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 12 })
        
        Row() {
          ForEach([
            SoundFieldMode.STEREO,
            SoundFieldMode.SURROUND,
            SoundFieldMode.CONCERT,
            SoundFieldMode.LIVE
          ], (mode: SoundFieldMode) => {
            Text(this.carAudioService.getSoundFieldModeName(mode))
              .fontColor(this.audioConfig.soundFieldMode === mode ? '#000000' : '#FFFFFF')
              .fontSize(13)
              .fontWeight(FontWeight.Medium)
              .textAlign(TextAlign.Center)
              .layoutWeight(1)
              .height(35)
              .backgroundColor(this.audioConfig.soundFieldMode === mode ? '#00D4FF' : 'rgba(255, 255, 255, 0.1)')
              .borderRadius(6)
              .margin({ left: 3, right: 3 })
              .onClick(() => {
                this.carAudioService.setSoundFieldMode(mode);
              })
          })
        }
        .width('100%')
      }
      .width('100%')
      .padding(15)
      .backgroundColor('rgba(0, 212, 255, 0.08)')
      .borderRadius(15)
    }
  }

  @Builder
  buildAdvancedControls() {
    Column() {
      // 音频增强控制
      Column() {
        Text('🔊 音频增强')
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 15 })
        
        // 低音增强
        Row() {
          Text('低音增强')
            .fontColor('#FFFFFF')
            .fontSize(14)
            .width(80)
          
          Slider({
            value: this.audioConfig.enhancement.bassBoost,
            min: 0,
            max: 100,
            style: SliderStyle.OutSet
          })
          .trackColor('#333333')
          .selectedColor('#00D4FF')
          .blockColor('#FFFFFF')
          .layoutWeight(1)
          .height(30)
          .margin({ left: 10, right: 10 })
          .onChange((value: number) => {
            this.carAudioService.setAudioEnhancement({ bassBoost: value });
          })
          
          Text(`${Math.round(this.audioConfig.enhancement.bassBoost)}%`)
            .fontColor('#00D4FF')
            .fontSize(14)
            .width(40)
        }
        .width('100%')
        .margin({ bottom: 12 })
        
        // 高音增强
        Row() {
          Text('高音增强')
            .fontColor('#FFFFFF')
            .fontSize(14)
            .width(80)
          
          Slider({
            value: this.audioConfig.enhancement.trebleBoost,
            min: 0,
            max: 100,
            style: SliderStyle.OutSet
          })
          .trackColor('#333333')
          .selectedColor('#00D4FF')
          .blockColor('#FFFFFF')
          .layoutWeight(1)
          .height(30)
          .margin({ left: 10, right: 10 })
          .onChange((value: number) => {
            this.carAudioService.setAudioEnhancement({ trebleBoost: value });
          })
          
          Text(`${Math.round(this.audioConfig.enhancement.trebleBoost)}%`)
            .fontColor('#00D4FF')
            .fontSize(14)
            .width(40)
        }
        .width('100%')
        .margin({ bottom: 15 })
        
        // 音频开关选项
        Column() {
          this.buildAudioToggle('虚拟环绕声', this.audioConfig.enhancement.virtualSurround, (value: boolean) => {
            this.carAudioService.setAudioEnhancement({ virtualSurround: value });
          })
          
          this.buildAudioToggle('响度补偿', this.audioConfig.enhancement.loudnessCompensation, (value: boolean) => {
            this.carAudioService.setAudioEnhancement({ loudnessCompensation: value });
          })
          
          this.buildAudioToggle('动态范围控制', this.audioConfig.enhancement.dynamicRangeControl, (value: boolean) => {
            this.carAudioService.setAudioEnhancement({ dynamicRangeControl: value });
          })
        }
      }
      .width('100%')
      .padding(15)
      .backgroundColor('rgba(0, 212, 255, 0.08)')
      .borderRadius(15)
      .margin({ top: 15, bottom: 15 })

      // 平衡控制
      Column() {
        Text('⚖️ 声音平衡')
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 15 })
        
        // 左右平衡
        Row() {
          Text('左右平衡')
            .fontColor('#FFFFFF')
            .fontSize(14)
            .width(80)
          
          Text('L')
            .fontColor('#CCCCCC')
            .fontSize(12)
            .width(15)
          
          Slider({
            value: this.audioConfig.balance + 50, // 转换为0-100范围
            min: 0,
            max: 100,
            style: SliderStyle.OutSet
          })
          .trackColor('#333333')
          .selectedColor('#00D4FF')
          .blockColor('#FFFFFF')
          .layoutWeight(1)
          .height(30)
          .margin({ left: 8, right: 8 })
          .onChange((value: number) => {
            this.carAudioService.setBalance(value - 50); // 转换回-50到50范围
          })
          
          Text('R')
            .fontColor('#CCCCCC')
            .fontSize(12)
            .width(15)
          
          Text(`${this.audioConfig.balance > 0 ? 'R' : this.audioConfig.balance < 0 ? 'L' : 'C'}${Math.abs(this.audioConfig.balance)}`)
            .fontColor('#00D4FF')
            .fontSize(14)
            .width(35)
        }
        .width('100%')
        .margin({ bottom: 12 })
        
        // 前后平衡
        Row() {
          Text('前后平衡')
            .fontColor('#FFFFFF')
            .fontSize(14)
            .width(80)
          
          Text('F')
            .fontColor('#CCCCCC')
            .fontSize(12)
            .width(15)
          
          Slider({
            value: this.audioConfig.fade + 50, // 转换为0-100范围
            min: 0,
            max: 100,
            style: SliderStyle.OutSet
          })
          .trackColor('#333333')
          .selectedColor('#00D4FF')
          .blockColor('#FFFFFF')
          .layoutWeight(1)
          .height(30)
          .margin({ left: 8, right: 8 })
          .onChange((value: number) => {
            this.carAudioService.setFade(value - 50); // 转换回-50到50范围
          })
          
          Text('R')
            .fontColor('#CCCCCC')
            .fontSize(12)
            .width(15)
          
          Text(`${this.audioConfig.fade > 0 ? 'R' : this.audioConfig.fade < 0 ? 'F' : 'C'}${Math.abs(this.audioConfig.fade)}`)
            .fontColor('#00D4FF')
            .fontSize(14)
            .width(35)
        }
        .width('100%')
      }
      .width('100%')
      .padding(15)
      .backgroundColor('rgba(0, 212, 255, 0.08)')
      .borderRadius(15)
    }
    .width('100%')
  }

  @Builder
  buildAudioToggle(title: string, isOn: boolean, onChange: (value: boolean) => void) {
    Row() {
      Text(title)
        .fontColor('#FFFFFF')
        .fontSize(14)
        .layoutWeight(1)
      
      Toggle({ type: ToggleType.Switch, isOn: isOn })
        .selectedColor('#00D4FF')
        .switchPointColor('#FFFFFF')
        .onChange(onChange)
    }
    .width('100%')
    .height(40)
    .padding({ left: 5, right: 5 })
  }
}
