/**
 * 音乐数据管理服务
 * 负责音乐数据的存储、检索和管理
 */

import { preferences } from '@kit.ArkData';
import { MusicInfo, UserPreferences, PlayMode } from '../common/MusicTypes';
import { common } from '@kit.AbilityKit';
import { KugouMusicService } from './KugouMusicService';

export class MusicDataService {
  private context: common.UIAbilityContext | null = null;
  private preferencesStore: preferences.Preferences | null = null;
  private userPrefs: UserPreferences;

  constructor(context?: common.UIAbilityContext) {
    this.context = context || null;
    this.userPrefs = this.getDefaultPreferences();
  }

  /**
   * 获取默认用户偏好设置
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      playMode: PlayMode.SEQUENCE,
      volume: 50,
      favoriteList: [],
      likedList: [],
      recentPlayList: [],
      lastPlayedId: undefined,
      playHistory: []
    };
  }

  /**
   * 初始化数据存储
   */
  public async initDataStore(): Promise<void> {
    if (!this.context) {
      console.warn('Context未设置，无法初始化数据存储');
      return;
    }

    try {
      this.preferencesStore = await preferences.getPreferences(this.context, 'music_preferences');
      await this.loadUserPreferences();
      console.info('音乐数据存储初始化成功');
    } catch (error) {
      console.error('初始化数据存储失败:', error);
    }
  }

  /**
   * 加载用户偏好设置
   */
  private async loadUserPreferences(): Promise<void> {
    if (!this.preferencesStore) return;

    try {
      const playMode = await this.preferencesStore.get('playMode', PlayMode.SEQUENCE) as PlayMode;
      const volume = await this.preferencesStore.get('volume', 50) as number;
      const favoriteList = await this.preferencesStore.get('favoriteList', []) as string[];
      const likedList = await this.preferencesStore.get('likedList', []) as string[];
      const recentPlayList = await this.preferencesStore.get('recentPlayList', []) as string[];

      this.userPrefs = {
        playMode,
        volume,
        favoriteList,
        likedList,
        recentPlayList,
        lastPlayedId: undefined,
        playHistory: []
      };
    } catch (error) {
      console.error('加载用户偏好设置失败:', error);
    }
  }

  /**
   * 保存用户偏好设置
   */
  private async saveUserPreferences(): Promise<void> {
    if (!this.preferencesStore) return;

    try {
      await this.preferencesStore.put('playMode', this.userPrefs.playMode);
      await this.preferencesStore.put('volume', this.userPrefs.volume);
      await this.preferencesStore.put('favoriteList', this.userPrefs.favoriteList);
      await this.preferencesStore.put('likedList', this.userPrefs.likedList);
      await this.preferencesStore.put('recentPlayList', this.userPrefs.recentPlayList);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('保存用户偏好设置失败:', error);
    }
  }

  /**
   * 初始化服务（兼容性方法）
   */
  public async initialize(): Promise<void> {
    await this.initDataStore();
  }

  /**
   * 获取所有音乐（兼容性方法）
   */
  public getAllMusic(): MusicInfo[] {
    return this.getDefaultMusicList();
  }

  /**
   * 获取示例音乐列表
   */
  public getDefaultMusicList(): MusicInfo[] {
    // 使用酷狗音乐服务提供的示例网络音乐列表
    return KugouMusicService.getExampleNetworkMusicList();
  }

  /**
   * 搜索网络音乐
   * @param keyword 搜索关键词
   * @param page 页码
   * @param pageSize 每页数量
   */
  public async searchNetworkMusic(keyword: string, page: number = 1, pageSize: number = 20): Promise<MusicInfo[]> {
    try {
      console.log(`搜索网络音乐: ${keyword}, 页码: ${page}, 每页: ${pageSize}`);
      const musicList = await KugouMusicService.searchMusic(keyword, page, pageSize);
      console.log(`搜索到 ${musicList.length} 首音乐`);
      return musicList;
    } catch (error) {
      console.error('搜索网络音乐失败:', error);
      return [];
    }
  }

  /**
   * 获取热门网络音乐
   */
  public async getHotNetworkMusic(): Promise<MusicInfo[]> {
    try {
      console.log('获取热门网络音乐');
      const musicList = await KugouMusicService.getHotMusic();
      console.log(`获取到 ${musicList.length} 首热门音乐`);
      return musicList;
    } catch (error) {
      console.error('获取热门音乐失败:', error);
      return this.getDefaultMusicList(); // 失败时返回默认列表
    }
  }

  /**
   * 根据艺术家获取网络音乐
   * @param artist 艺术家名称
   */
  public async getMusicByArtist(artist: string): Promise<MusicInfo[]> {
    try {
      console.log(`根据艺术家获取音乐: ${artist}`);
      const musicList = await KugouMusicService.getMusicByArtist(artist);
      console.log(`获取到 ${musicList.length} 首${artist}的音乐`);
      return musicList;
    } catch (error) {
      console.error(`获取${artist}的音乐失败:`, error);
      return [];
    }
  }

  /**
   * 获取完整的音乐信息（包含播放URL和封面）
   * @param musicInfo 基础音乐信息
   */
  public async getCompleteMusic(musicInfo: MusicInfo): Promise<MusicInfo> {
    try {
      if (musicInfo.isNetworkResource && (!musicInfo.uri || !musicInfo.coverUri)) {
        console.log(`获取完整音乐信息: ${musicInfo.title}`);
        const completeMusic = await KugouMusicService.getCompleteMusic(musicInfo);
        console.log(`完整音乐信息获取${completeMusic.uri ? '成功' : '失败'}`);
        return completeMusic;
      }
      return musicInfo;
    } catch (error) {
      console.error('获取完整音乐信息失败:', error);
      return musicInfo;
    }
  }

  /**
   * 检查网络连接状态
   */
  public async checkNetworkConnection(): Promise<boolean> {
    try {
      const isConnected = await KugouMusicService.checkNetworkConnection();
      console.log(`网络连接状态: ${isConnected ? '已连接' : '未连接'}`);
      return isConnected;
    } catch (error) {
      console.error('检查网络连接失败:', error);
      return false;
    }
  }

  /**
   * 点赞歌曲
   */
  public async likeSong(musicId: string): Promise<void> {
    if (!this.userPrefs.likedList.includes(musicId)) {
      this.userPrefs.likedList.push(musicId);
      await this.saveUserPreferences();
    }
  }

  /**
   * 取消点赞
   */
  public async unlikeSong(musicId: string): Promise<void> {
    const index = this.userPrefs.likedList.indexOf(musicId);
    if (index > -1) {
      this.userPrefs.likedList.splice(index, 1);
      await this.saveUserPreferences();
    }
  }

  /**
   * 收藏歌曲
   */
  public async favoriteSong(musicId: string): Promise<void> {
    if (!this.userPrefs.favoriteList.includes(musicId)) {
      this.userPrefs.favoriteList.push(musicId);
      await this.saveUserPreferences();
    }
  }

  /**
   * 取消收藏
   */
  public async unfavoriteSong(musicId: string): Promise<void> {
    const index = this.userPrefs.favoriteList.indexOf(musicId);
    if (index > -1) {
      this.userPrefs.favoriteList.splice(index, 1);
      await this.saveUserPreferences();
    }
  }

  /**
   * 添加到最近播放
   */
  public async addToRecentPlay(musicId: string): Promise<void> {
    // 移除已存在的记录
    const index = this.userPrefs.recentPlayList.indexOf(musicId);
    if (index > -1) {
      this.userPrefs.recentPlayList.splice(index, 1);
    }
    
    // 添加到开头
    this.userPrefs.recentPlayList.unshift(musicId);
    
    // 限制最近播放列表长度
    if (this.userPrefs.recentPlayList.length > 50) {
      this.userPrefs.recentPlayList = this.userPrefs.recentPlayList.slice(0, 50);
    }
    
    await this.saveUserPreferences();
  }

  /**
   * 设置播放模式
   */
  public async setPlayMode(mode: PlayMode): Promise<void> {
    this.userPrefs.playMode = mode;
    await this.saveUserPreferences();
  }

  /**
   * 设置音量
   */
  public async setVolume(volume: number): Promise<void> {
    this.userPrefs.volume = Math.max(0, Math.min(100, volume));
    await this.saveUserPreferences();
  }

  /**
   * 检查歌曲是否被点赞
   */
  public isLiked(musicId: string): boolean {
    return this.userPrefs.likedList.includes(musicId);
  }

  /**
   * 检查歌曲是否被收藏
   */
  public isFavorite(musicId: string): boolean {
    return this.userPrefs.favoriteList.includes(musicId);
  }

  /**
   * 获取用户偏好设置
   */
  public getUserPreferences(): UserPreferences {
    return {
      favoriteList: this.userPrefs.favoriteList.slice(),
      likedList: this.userPrefs.likedList.slice(),
      playMode: this.userPrefs.playMode,
      volume: this.userPrefs.volume,
      lastPlayedId: this.userPrefs.lastPlayedId,
      playHistory: this.userPrefs.playHistory.slice(),
      recentPlayList: this.userPrefs.recentPlayList.slice()
    };
  }

  /**
   * 获取收藏列表
   */
  public getFavoriteList(): string[] {
    return this.userPrefs.favoriteList.slice();
  }

  /**
   * 获取点赞列表
   */
  public getLikedList(): string[] {
    return [...this.userPrefs.likedList];
  }

  /**
   * 获取播放模式
   */
  public getPlayMode(): PlayMode {
    return this.userPrefs.playMode;
  }

  /**
   * 获取音量
   */
  public getVolume(): number {
    return this.userPrefs.volume;
  }

  /**
   * 获取最近播放列表
   */
  public getRecentPlayList(): string[] {
    return this.userPrefs.recentPlayList.slice();
  }

  /**
   * 格式化时间显示
   */
  public static formatTime(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * 释放资源
   */
  public async release(): Promise<void> {
    if (this.preferencesStore) {
      try {
        await this.saveUserPreferences();
      } catch (error) {
        console.error('释放数据服务失败:', error);
      }
    }
  }
}
