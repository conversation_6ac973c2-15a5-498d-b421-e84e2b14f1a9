/**
 * 音乐播放器服务类
 * 负责音乐播放的核心功能实现
 */

import { media } from '@kit.MediaKit';
import { MusicInfo, PlayState, PlayMode, PlayerEvent, PlayerState } from '../common/MusicTypes';
import { MusicHistoryService } from './MusicHistoryService';

export class MusicPlayerService {
  private avPlayer: media.AVPlayer | null = null;
  private playlist: MusicInfo[] = [];
  private currentIndex: number = 0;
  private playState: PlayState = PlayState.STOPPED;
  private playMode: PlayMode = PlayMode.SEQUENCE;
  private currentPosition: number = 0;
  private duration: number = 0;
  private volume: number = 50;
  private isLoading: boolean = false;
  private eventListeners: Map<PlayerEvent, Function[]> = new Map<PlayerEvent, Function[]>();
  private positionTimer: number = 0;
  private simulationTimer: number = 0; // 模拟播放定时器
  private isSimulationMode: boolean = false; // 禁用模拟模式，使用真实播放
  private historyService: MusicHistoryService = MusicHistoryService.getInstance();
  private playStartTime: number = 0; // 播放开始时间

  constructor() {
    this.initEventListeners();
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    // 手动初始化所有事件类型
    const events: PlayerEvent[] = [
      PlayerEvent.PLAY,
      PlayerEvent.PAUSE,
      PlayerEvent.STOP,
      PlayerEvent.NEXT,
      PlayerEvent.PREVIOUS,
      PlayerEvent.SEEK,
      PlayerEvent.TIME_UPDATE,
      PlayerEvent.DURATION_CHANGE,
      PlayerEvent.STATE_CHANGE,
      PlayerEvent.ERROR
    ];

    events.forEach((event: PlayerEvent) => {
      this.eventListeners.set(event, []);
    });
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: PlayerEvent, callback: Function): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.push(callback);
    this.eventListeners.set(event, listeners);
  }

  /**
   * 添加事件监听器（兼容性方法）
   */
  public on(event: PlayerEvent, callback: Function): void {
    this.addEventListener(event, callback);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: PlayerEvent, callback: Function): void {
    const listeners = this.eventListeners.get(event) || [];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(event: PlayerEvent, data?: Object): void {
    const listeners: Function[] = this.eventListeners.get(event) || [];
    listeners.forEach((callback: Function): void => {
      callback(data);
    });
  }

  /**
   * 初始化服务（兼容性方法）
   */
  public async initialize(): Promise<void> {
    await this.initPlayer();
  }

  /**
   * 初始化播放器
   */
  public async initPlayer(): Promise<void> {
    try {
      if (this.avPlayer) {
        await this.avPlayer.release();
      }
      
      this.avPlayer = await media.createAVPlayer();
      this.setupPlayerCallbacks();
      console.info('音乐播放器初始化成功');
    } catch (error) {
      console.error('初始化播放器失败:', error);
      this.playState = PlayState.ERROR;
      this.emitEvent(PlayerEvent.ERROR, error);
    }
  }

  /**
   * 设置播放器回调
   */
  private setupPlayerCallbacks(): void {
    if (!this.avPlayer) return;

    // 播放状态变化回调
    this.avPlayer.on('stateChange', (state) => {
      console.info('播放器状态变化:', state);
      this.handleStateChange(state);
    });

    // 时长变化回调
    this.avPlayer.on('durationUpdate', (duration) => {
      this.duration = duration;
      this.emitEvent(PlayerEvent.DURATION_CHANGE, duration);
    });

    // 播放完成回调
    this.avPlayer.on('endOfStream', () => {
      console.info('播放完成');
      this.handlePlayComplete();
    });

    // 错误回调
    this.avPlayer.on('error', (error) => {
      console.error('播放器错误:', error);
      this.playState = PlayState.ERROR;
      this.emitEvent(PlayerEvent.ERROR, error);
    });
  }

  /**
   * 处理播放器状态变化
   */
  private handleStateChange(state: string): void {
    switch (state) {
      case 'prepared':
        this.isLoading = false;
        this.playState = PlayState.PAUSED;
        break;
      case 'playing':
        this.playState = PlayState.PLAYING;
        this.startPositionTimer();
        break;
      case 'paused':
        this.playState = PlayState.PAUSED;
        this.stopPositionTimer();
        break;
      case 'stopped':
        this.playState = PlayState.STOPPED;
        this.stopPositionTimer();
        this.currentPosition = 0;
        break;
    }
    this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
  }

  /**
   * 开始位置定时器
   */
  private startPositionTimer(): void {
    this.stopPositionTimer();
    this.positionTimer = setInterval(async () => {
      if (this.avPlayer && this.playState === PlayState.PLAYING) {
        try {
          this.currentPosition = this.avPlayer.currentTime;
          this.emitEvent(PlayerEvent.TIME_UPDATE, this.currentPosition);
        } catch (error) {
          console.error('获取播放位置失败:', error);
        }
      }
    }, 1000);
  }

  /**
   * 停止位置定时器
   */
  private stopPositionTimer(): void {
    if (this.positionTimer) {
      clearInterval(this.positionTimer);
      this.positionTimer = 0;
    }
  }

  /**
   * 处理播放完成
   */
  private handlePlayComplete(): void {
    switch (this.playMode) {
      case PlayMode.SINGLE:
        // 单曲循环，重新播放当前歌曲
        this.seekTo(0);
        this.play();
        break;
      case PlayMode.SEQUENCE:
        // 顺序播放，播放下一首
        if (this.currentIndex < this.playlist.length - 1) {
          this.next();
        } else {
          this.stop();
        }
        break;
      case PlayMode.LOOP:
        // 列表循环，播放下一首（到末尾时回到开头）
        this.next();
        break;
      case PlayMode.RANDOM:
        // 随机播放
        this.playRandom();
        break;
    }
  }

  /**
   * 加载音乐
   */
  public async loadMusic(music: MusicInfo): Promise<void> {
    try {
      console.info('🎵 开始加载音乐:', music.title, '路径:', music.uri);
      this.isLoading = true;
      this.playState = PlayState.LOADING;
      this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);

      // 尝试多种路径格式，优化本地音频文件加载
      const fileName = music.uri.split('/').pop() || music.uri;
      const pathFormats = [
        music.uri,                                    // 原始路径
        `resource://RAWFILE/music/${fileName}`,      // resource://格式（推荐）
        `rawfile://music/${fileName}`,               // rawfile://格式
        `fd://rawfile/music/${fileName}`,            // fd://格式
        `file://rawfile/music/${fileName}`,          // file://格式
        fileName,                                     // 直接文件名
        `music/${fileName}`,                         // 相对路径
        `/music/${fileName}`                         // 绝对路径格式
      ];

      let loadSuccess = false;

      for (let i = 0; i < pathFormats.length && !loadSuccess; i++) {
        const currentPath = pathFormats[i];
        console.info(`🎵 尝试路径格式 ${i + 1}/${pathFormats.length}:`, currentPath);

        try {
          // 真实播放模式
          if (!this.avPlayer) {
            console.info('🎵 播放器未初始化，开始初始化...');
            await this.initPlayer();
          }

          if (this.avPlayer) {
            console.info('🎵 重置播放器...');
            await this.avPlayer.reset();

            console.info('🎵 设置音乐URL:', currentPath);
            this.avPlayer.url = currentPath;

            console.info('🎵 准备播放器...');
            await this.avPlayer.prepare();

            console.info('✅ 音乐加载成功:', music.title, '使用路径:', currentPath);
            loadSuccess = true;
            break;
          }
        } catch (pathError) {
          console.warn(`⚠️ 路径格式 ${i + 1} 失败:`, pathError);
          if (i === pathFormats.length - 1) {
            throw new Error('所有音频路径格式都无法加载'); // 最后一个格式也失败，抛出错误
          }
        }
      }

      if (loadSuccess) {
        this.isLoading = false;
        this.playState = PlayState.PAUSED;
        this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
      } else {
        throw new Error('所有路径格式都无法加载音乐');
      }
    } catch (error) {
      console.error('❌ 加载音乐失败:', error);
      console.error('❌ 音乐路径:', music.uri);
      console.error('❌ 错误详情:', JSON.stringify(error));

      this.isLoading = false;
      this.playState = PlayState.ERROR;
      this.emitEvent(PlayerEvent.ERROR, error);

      // 如果真实播放失败，启用模拟模式作为备用
      console.warn('⚠️ 启用模拟播放模式作为备用');
      this.isSimulationMode = true;
      this.duration = music.duration;
      this.currentPosition = 0;
      this.emitEvent(PlayerEvent.DURATION_CHANGE, this.duration);
      this.playState = PlayState.PAUSED;
      this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
    }
  }

  /**
   * 播放
   */
  public async play(): Promise<void> {
    try {
      // 记录播放开始时间
      this.playStartTime = Date.now();

      if (this.isSimulationMode) {
        // 模拟播放模式
        this.playState = PlayState.PLAYING;
        this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
        this.emitEvent(PlayerEvent.PLAY);
        this.startSimulationTimer();
        console.info('开始模拟播放');
      } else {
        // 真实播放模式
        if (!this.avPlayer) return;
        await this.avPlayer.play();
        this.emitEvent(PlayerEvent.PLAY);
      }
    } catch (error) {
      console.error('播放失败:', error);
      this.emitEvent(PlayerEvent.ERROR, error);
    }
  }

  /**
   * 暂停
   */
  public async pause(): Promise<void> {
    try {
      // 记录播放历史（暂停时）
      this.recordPlayHistory(false, '用户暂停');

      if (this.isSimulationMode) {
        // 模拟暂停模式
        this.playState = PlayState.PAUSED;
        this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
        this.emitEvent(PlayerEvent.PAUSE);
        this.stopSimulationTimer();
        console.info('模拟播放暂停');
      } else {
        // 真实暂停模式
        if (!this.avPlayer) return;
        await this.avPlayer.pause();
        this.emitEvent(PlayerEvent.PAUSE);
      }
    } catch (error) {
      console.error('暂停失败:', error);
      this.emitEvent(PlayerEvent.ERROR, error);
    }
  }

  /**
   * 停止
   */
  public async stop(): Promise<void> {
    if (!this.avPlayer) return;

    try {
      await this.avPlayer.stop();
      this.emitEvent(PlayerEvent.STOP);
    } catch (error) {
      console.error('停止失败:', error);
      this.emitEvent(PlayerEvent.ERROR, error);
    }
  }

  /**
   * 跳转到指定位置
   */
  public async seekTo(position: number): Promise<void> {
    if (!this.avPlayer) return;

    try {
      await this.avPlayer.seek(position);
      this.currentPosition = position;
      this.emitEvent(PlayerEvent.SEEK, position);
    } catch (error) {
      console.error('跳转失败:', error);
      this.emitEvent(PlayerEvent.ERROR, error);
    }
  }

  /**
   * 下一首
   */
  public async next(): Promise<void> {
    if (this.playlist.length === 0) return;

    // 记录当前歌曲播放历史（切换到下一首）
    this.recordPlayHistory(false, '切换到下一首');

    let nextIndex: number;
    if (this.playMode === PlayMode.RANDOM) {
      nextIndex = Math.floor(Math.random() * this.playlist.length);
    } else {
      nextIndex = (this.currentIndex + 1) % this.playlist.length;
    }

    await this.playByIndex(nextIndex);
    this.emitEvent(PlayerEvent.NEXT);
  }

  /**
   * 上一首
   */
  public async previous(): Promise<void> {
    if (this.playlist.length === 0) return;

    // 记录当前歌曲播放历史（切换到上一首）
    this.recordPlayHistory(false, '切换到上一首');

    let prevIndex: number;
    if (this.playMode === PlayMode.RANDOM) {
      prevIndex = Math.floor(Math.random() * this.playlist.length);
    } else {
      prevIndex = this.currentIndex === 0 ? this.playlist.length - 1 : this.currentIndex - 1;
    }

    await this.playByIndex(prevIndex);
    this.emitEvent(PlayerEvent.PREVIOUS);
  }

  /**
   * 随机播放
   */
  private async playRandom(): Promise<void> {
    if (this.playlist.length === 0) return;
    
    const randomIndex = Math.floor(Math.random() * this.playlist.length);
    await this.playByIndex(randomIndex);
  }

  /**
   * 根据索引播放
   */
  public async playByIndex(index: number): Promise<void> {
    if (index < 0 || index >= this.playlist.length) return;

    this.currentIndex = index;
    const music = this.playlist[index];
    await this.loadMusic(music);
    await this.play();
  }

  // Getter方法
  public getCurrentMusic(): MusicInfo | null {
    return this.playlist[this.currentIndex] || null;
  }

  public getPlayState(): PlayState {
    return this.playState;
  }

  public getPlayMode(): PlayMode {
    return this.playMode;
  }

  public setPlayMode(mode: PlayMode): void {
    this.playMode = mode;
  }

  public getPlaylist(): MusicInfo[] {
    return [...this.playlist];
  }

  public setPlaylist(playlist: MusicInfo[]): void {
    this.playlist = [...playlist];
  }

  public addToPlaylist(music: MusicInfo): void {
    this.playlist.push(music);
  }

  public getCurrentPosition(): number {
    return this.currentPosition;
  }

  public getDuration(): number {
    return this.duration;
  }

  public getVolume(): number {
    return this.volume;
  }

  public async setVolume(volume: number): Promise<void> {
    this.volume = Math.max(0, Math.min(100, volume));
    if (this.avPlayer && !this.isSimulationMode) {
      try {
        await this.avPlayer.setVolume(this.volume / 100);
        console.info(`音量设置为: ${this.volume}%`);
      } catch (error) {
        console.error('设置音量失败:', error);
      }
    }
    this.emitEvent(PlayerEvent.VOLUME_CHANGE, this.volume);
  }

  public getLoadingState(): boolean {
    return this.isLoading;
  }

  /**
   * 获取播放器状态
   */
  public getPlayerState(): PlayerState {
    return {
      currentMusic: this.getCurrentMusic(),
      playState: this.playState,
      playMode: this.playMode,
      currentPosition: this.currentPosition,
      duration: this.duration,
      volume: this.volume,
      isLoading: this.isLoading
    };
  }

  /**
   * 启动模拟播放定时器
   */
  private startSimulationTimer(): void {
    this.stopSimulationTimer(); // 先清除已有定时器

    this.simulationTimer = setInterval(() => {
      if (this.playState === PlayState.PLAYING) {
        this.currentPosition += 1000; // 每秒增加1000毫秒
        this.emitEvent(PlayerEvent.TIME_UPDATE, this.currentPosition);

        // 检查是否播放完成
        if (this.currentPosition >= this.duration) {
          this.currentPosition = this.duration;
          this.playState = PlayState.STOPPED;
          this.emitEvent(PlayerEvent.STATE_CHANGE, this.playState);
          this.stopSimulationTimer();

          // 根据播放模式决定下一步操作
          if (this.playMode === PlayMode.SINGLE) {
            // 单曲循环：重新开始播放
            this.currentPosition = 0;
            setTimeout(() => {
              this.play();
            }, 100);
          } else if (this.playMode === PlayMode.LOOP || this.playMode === PlayMode.SEQUENCE) {
            // 列表循环或顺序播放：播放下一首
            setTimeout(() => {
              this.next();
            }, 100);
          }
        }
      }
    }, 1000);
  }

  /**
   * 停止模拟播放定时器
   */
  private stopSimulationTimer(): void {
    if (this.simulationTimer) {
      clearInterval(this.simulationTimer);
      this.simulationTimer = 0;
    }
  }

  /**
   * 获取推荐音乐
   */
  public getRecommendedMusic(): MusicInfo[] {
    // 使用历史记录服务获取推荐音乐
    const currentMusic = this.getCurrentMusic();
    return this.historyService.getRecommendedMusic(currentMusic, this.playlist, 5)
      .map(item => item.music);
  }

  /**
   * 记录播放历史
   */
  private recordPlayHistory(isCompleted: boolean, skipReason?: string): void {
    const currentMusic = this.getCurrentMusic();
    if (!currentMusic || this.playStartTime === 0) return;

    const playDuration = Date.now() - this.playStartTime;

    // 只记录播放时长超过5秒的记录
    if (playDuration > 5000) {
      this.historyService.addPlayRecord(currentMusic, playDuration, isCompleted, skipReason);
    }

    // 重置播放开始时间
    this.playStartTime = 0;
  }

  /**
   * 获取播放历史
   */
  public getPlayHistory(limit?: number) {
    return this.historyService.getPlayHistory(limit);
  }

  /**
   * 获取播放统计
   */
  public getPlayStatistics() {
    return this.historyService.getPlayStatistics();
  }

  /**
   * 获取最近播放
   */
  public getRecentlyPlayed(limit?: number) {
    return this.historyService.getRecentlyPlayed(limit);
  }

  /**
   * 测试音频文件路径
   */
  public async testAudioPath(uri: string): Promise<boolean> {
    try {
      console.info('🧪 测试音频路径:', uri);

      if (!this.avPlayer) {
        await this.initPlayer();
      }

      if (this.avPlayer) {
        await this.avPlayer.reset();
        this.avPlayer.url = uri;
        await this.avPlayer.prepare();
        console.info('✅ 路径测试成功:', uri);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ 路径测试失败:', uri, error);
      return false;
    }
  }

  /**
   * 批量测试所有音频路径格式
   */
  public async testAllAudioFormats(): Promise<void> {
    const testFileName = 'song1.mp3';
    const formats = [
      `rawfile://music/${testFileName}`,
      `resource://RAWFILE/music/${testFileName}`,
      `fd://rawfile/music/${testFileName}`,
      `file://rawfile/music/${testFileName}`,
      testFileName,
      `music/${testFileName}`,
      `/music/${testFileName}`
    ];

    console.info('🧪 开始批量测试音频路径格式...');

    for (let i = 0; i < formats.length; i++) {
      const format = formats[i];
      const success = await this.testAudioPath(format);
      console.info(`${success ? '✅' : '❌'} 格式 ${i + 1}/${formats.length}: ${format}`);

      if (success) {
        console.info('🎉 找到可用的音频路径格式:', format);
        break;
      }
    }
  }

  /**
   * 释放资源
   */
  public async release(): Promise<void> {
    this.stopPositionTimer();
    this.stopSimulationTimer();
    if (this.avPlayer) {
      try {
        await this.avPlayer.release();
        this.avPlayer = null;
      } catch (error) {
        console.error('释放播放器失败:', error);
      }
    }
    this.eventListeners.clear();
  }
}
