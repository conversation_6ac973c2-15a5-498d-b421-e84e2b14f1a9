/**
 * 音乐推荐和历史记录面板组件
 * 显示推荐音乐、播放历史、播放统计等信息
 */

import { MusicInfo } from '../common/MusicTypes';
import { MusicHistoryService, PlayHistoryItem, PlayStatistics, RecommendedMusic } from '../services/MusicHistoryService';

@Component
export struct MusicRecommendationPanel {
  @State private historyService: MusicHistoryService = MusicHistoryService.getInstance();
  @State private playHistory: PlayHistoryItem[] = [];
  @State private playStatistics: PlayStatistics = {
    totalPlayTime: 0,
    totalSongs: 0,
    favoriteGenre: '未知',
    mostPlayedSong: null,
    averagePlayTime: 0,
    todayPlayTime: 0,
    weekPlayTime: 0
  };
  @State private recommendedMusic: RecommendedMusic[] = [];
  @State private showHistory: boolean = true;
  @State private showRecommendations: boolean = true;
  @State private showStatistics: boolean = false;

  // 回调函数
  onMusicSelect?: (music: MusicInfo) => void;

  aboutToAppear() {
    this.loadData();
    
    // 监听历史记录变化
    this.historyService.addHistoryListener((history: PlayHistoryItem[]) => {
      this.loadData();
    });
  }

  build() {
    Column() {
      // 🎨 美化标题栏
      Row() {
        Row() {
          Text('🎵')
            .fontSize(24)
            .margin({ right: 10 })
          Text('音乐发现')
            .fontColor('#00D4FF')
            .fontSize(22)
            .fontWeight(FontWeight.Bold)
        }
        .alignItems(VerticalAlign.Center)

        Blank()

        // 功能切换按钮 - 美化样式
        Row() {
          Text('历史')
            .fontColor(this.showHistory ? '#00D4FF' : '#CCCCCC')
            .fontSize(14)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(this.showHistory ? 'rgba(0, 212, 255, 0.2)' : 'transparent')
            .borderRadius(6)
            .onClick(() => {
              this.showHistory = !this.showHistory;
            })
          
          Text('推荐')
            .fontColor(this.showRecommendations ? '#00D4FF' : '#CCCCCC')
            .fontSize(14)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(this.showRecommendations ? 'rgba(0, 212, 255, 0.2)' : 'transparent')
            .borderRadius(6)
            .margin({ left: 8 })
            .onClick(() => {
              this.showRecommendations = !this.showRecommendations;
            })
          
          Text('统计')
            .fontColor(this.showStatistics ? '#00D4FF' : '#CCCCCC')
            .fontSize(14)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(this.showStatistics ? 'rgba(0, 212, 255, 0.2)' : 'transparent')
            .borderRadius(6)
            .margin({ left: 8 })
            .onClick(() => {
              this.showStatistics = !this.showStatistics;
            })
        }
      }
      .width('100%')
      .margin({ bottom: 15 })

      // 播放统计
      if (this.showStatistics) {
        this.buildStatisticsSection()
      }

      // 推荐音乐
      if (this.showRecommendations) {
        this.buildRecommendationsSection()
      }

      // 播放历史
      if (this.showHistory) {
        this.buildHistorySection()
      }
    }
    .width('100%')
    .padding(15)
  }

  @Builder
  buildStatisticsSection() {
    Column() {
      Text('📊 播放统计')
        .fontColor('#00D4FF')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 12 })
      
      Grid() {
        GridItem() {
          this.buildStatCard('总播放时长', this.formatDuration(this.playStatistics.totalPlayTime), '🕐')
        }
        GridItem() {
          this.buildStatCard('播放歌曲数', `${this.playStatistics.totalSongs}首`, '🎵')
        }
        GridItem() {
          this.buildStatCard('今日播放', this.formatDuration(this.playStatistics.todayPlayTime), '📅')
        }
        GridItem() {
          this.buildStatCard('本周播放', this.formatDuration(this.playStatistics.weekPlayTime), '📆')
        }
      }
      .columnsTemplate('1fr 1fr')
      .rowsTemplate('1fr 1fr')
      .columnsGap(10)
      .rowsGap(10)
      .width('100%')
      .height(120)
      .margin({ bottom: 15 })

      // 最喜欢的音乐类型和最常播放的歌曲
      if (this.playStatistics.favoriteGenre !== '未知' || this.playStatistics.mostPlayedSong) {
        Column() {
          if (this.playStatistics.favoriteGenre !== '未知') {
            Text(`🎭 最喜欢的类型：${this.playStatistics.favoriteGenre}`)
              .fontColor('#FFFFFF')
              .fontSize(14)
              .margin({ bottom: 8 })
          }
          
          if (this.playStatistics.mostPlayedSong) {
            Text(`⭐ 最常播放：${this.playStatistics.mostPlayedSong.title} - ${this.playStatistics.mostPlayedSong.artist}`)
              .fontColor('#FFFFFF')
              .fontSize(14)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
          }
        }
        .width('100%')
        .padding(12)
        .backgroundColor('rgba(255, 255, 255, 0.05)')
        .borderRadius(10)
      }
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder
  buildStatCard(title: string, value: string, icon: string) {
    Column() {
      Text(icon)
        .fontSize(20)
        .margin({ bottom: 4 })
      
      Text(value)
        .fontColor('#00D4FF')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 2 })
      
      Text(title)
        .fontColor('#CCCCCC')
        .fontSize(12)
        .opacity(0.8)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .backgroundColor('rgba(0, 212, 255, 0.08)')
    .borderRadius(8)
  }

  @Builder
  buildRecommendationsSection() {
    Column() {
      Text('🎯 为您推荐')
        .fontColor('#00D4FF')
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 12 })
      
      if (this.recommendedMusic.length === 0) {
        Text('暂无推荐音乐')
          .fontColor('#CCCCCC')
          .fontSize(14)
          .opacity(0.6)
          .textAlign(TextAlign.Center)
          .width('100%')
          .height(60)
      } else {
        Column() {
          ForEach(this.recommendedMusic.slice(0, 5), (item: RecommendedMusic, index: number) => {
            Row() {
              // 推荐分数指示器
              Text(`${Math.round(item.score)}`)
                .fontColor('#00D4FF')
                .fontSize(12)
                .fontWeight(FontWeight.Bold)
                .width(30)
                .height(30)
                .textAlign(TextAlign.Center)
                .backgroundColor('rgba(0, 212, 255, 0.2)')
                .borderRadius(15)
                .margin({ right: 12 })
              
              Column() {
                Text(item.music.title)
                  .fontColor('#FFFFFF')
                  .fontSize(15)
                  .fontWeight(FontWeight.Medium)
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .width('100%')
                
                Text(`${item.music.artist} • ${item.reasonText}`)
                  .fontColor('#CCCCCC')
                  .fontSize(12)
                  .opacity(0.8)
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .width('100%')
                  .margin({ top: 2 })
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1)
              
              Text('▶️')
                .fontSize(16)
                .opacity(0.6)
            }
            .width('100%')
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .backgroundColor(index % 2 === 0 ? 'rgba(255, 255, 255, 0.03)' : 'transparent')
            .borderRadius(8)
            .onClick(() => {
              if (this.onMusicSelect) {
                this.onMusicSelect(item.music);
              }
            })
          })
        }
      }
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  @Builder
  buildHistorySection() {
    Column() {
      Row() {
        Text('🕒 播放历史')
          .fontColor('#00D4FF')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
        
        Blank()
        
        if (this.playHistory.length > 0) {
          Text('清空')
            .fontColor('#FF6B6B')
            .fontSize(12)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor('rgba(255, 107, 107, 0.2)')
            .borderRadius(6)
            .onClick(() => {
              this.historyService.clearHistory();
            })
        }
      }
      .width('100%')
      .margin({ bottom: 12 })
      
      if (this.playHistory.length === 0) {
        Text('暂无播放历史')
          .fontColor('#CCCCCC')
          .fontSize(14)
          .opacity(0.6)
          .textAlign(TextAlign.Center)
          .width('100%')
          .height(60)
      } else {
        Column() {
          ForEach(this.playHistory.slice(0, 8), (item: PlayHistoryItem, index: number) => {
            Row() {
              Text(this.formatTime(item.playTime))
                .fontColor('#CCCCCC')
                .fontSize(11)
                .opacity(0.6)
                .width(50)
              
              Column() {
                Text(item.music.title)
                  .fontColor('#FFFFFF')
                  .fontSize(14)
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .width('100%')
                
                Text(`${item.music.artist} • 播放${this.formatDuration(item.playDuration)}`)
                  .fontColor('#CCCCCC')
                  .fontSize(11)
                  .opacity(0.7)
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .width('100%')
                  .margin({ top: 2 })
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1)
              .margin({ left: 10 })
              
              Text(item.isCompleted ? '✅' : '⏸️')
                .fontSize(12)
                .opacity(0.6)
            }
            .width('100%')
            .padding({ left: 8, right: 8, top: 8, bottom: 8 })
            .backgroundColor(index % 2 === 0 ? 'rgba(255, 255, 255, 0.02)' : 'transparent')
            .borderRadius(6)
            .onClick(() => {
              if (this.onMusicSelect) {
                this.onMusicSelect(item.music);
              }
            })
          })
        }
      }
    }
    .width('100%')
  }

  private loadData(): void {
    this.playHistory = this.historyService.getPlayHistory(20);
    this.playStatistics = this.historyService.getPlayStatistics();
    // 推荐音乐需要当前播放的音乐和完整播放列表，这里暂时使用空数组
    this.recommendedMusic = this.historyService.getRecommendedMusic(null, [], 10);
  }

  private formatDuration(ms: number): string {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  private formatTime(timestamp: number): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  }
}
