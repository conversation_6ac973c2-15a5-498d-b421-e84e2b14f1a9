@echo off
chcp 65001 >nul
echo ========================================
echo    HarmonyOS 平板模拟器部署脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查DevEco Studio环境
echo [1/8] 检查DevEco Studio环境...
if not exist "%USERPROFILE%\.deveco-studio" (
    echo ❌ 错误：未找到DevEco Studio配置目录
    echo 请确保已正确安装DevEco Studio
    pause
    exit /b 1
)

:: 检查HarmonyOS SDK
echo [2/8] 检查HarmonyOS SDK...
set "SDK_PATH="
for /d %%i in ("%USERPROFILE%\AppData\Local\Huawei\Sdk\*") do (
    if exist "%%i\hmscore" set "SDK_PATH=%%i"
)

if "%SDK_PATH%"=="" (
    echo ❌ 错误：未找到HarmonyOS SDK
    echo 请在DevEco Studio中下载并安装HarmonyOS SDK
    pause
    exit /b 1
)

echo ✅ SDK路径: %SDK_PATH%

:: 设置环境变量
set "PATH=%SDK_PATH%\toolchains;%SDK_PATH%\hdc;%PATH%"

:: 检查项目结构
echo [3/8] 检查项目结构...
if not exist "entry\src\main\module.json5" (
    echo ❌ 错误：当前目录不是有效的HarmonyOS项目
    echo 请在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查平板配置
echo [4/8] 检查平板配置...
findstr /c:"tablet" entry\src\main\module.json5 >nul
if errorlevel 1 (
    echo ❌ 错误：项目未配置平板设备支持
    echo 请检查module.json5中的deviceTypes配置
    pause
    exit /b 1
)

echo ✅ 项目已配置平板设备支持

:: 检查智能入口页面
echo 检查智能入口页面...
if not exist "entry\src\main\ets\pages\SmartIndex.ets" (
    echo ⚠️ 警告：未找到智能入口页面 SmartIndex.ets
    echo 将使用标准入口页面
) else (
    echo ✅ 智能入口页面已就绪
)

if not exist "entry\src\main\ets\common\DeviceUtils.ets" (
    echo ⚠️ 警告：未找到设备工具类 DeviceUtils.ets
    echo 设备自动检测功能可能不可用
) else (
    echo ✅ 设备工具类已就绪
)

:: 清理并构建项目
echo [5/8] 清理并构建项目...
echo 正在清理项目...
if exist "build" rmdir /s /q "build" 2>nul
if exist "entry\build" rmdir /s /q "entry\build" 2>nul

echo 正在安装依赖...
call ohpm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo 正在构建项目...
call hvigorw assembleHap --mode module -p product=default -p buildMode=debug
if errorlevel 1 (
    echo ❌ 项目构建失败
    echo 请检查代码是否有语法错误
    pause
    exit /b 1
)

echo ✅ 项目构建成功

:: 检查平板模拟器
echo [6/8] 检查平板模拟器状态...
hdc list targets > temp_devices.txt 2>&1
set "TABLET_DEVICE="

:: 查找平板模拟器设备
for /f "tokens=*" %%i in (temp_devices.txt) do (
    echo %%i | findstr /i "tablet" >nul
    if not errorlevel 1 (
        set "TABLET_DEVICE=%%i"
        goto :found_tablet
    )
    echo %%i | findstr /i "emulator" >nul
    if not errorlevel 1 (
        echo %%i | findstr /i "pad" >nul
        if not errorlevel 1 (
            set "TABLET_DEVICE=%%i"
            goto :found_tablet
        )
    )
)

:: 如果没有找到专用平板模拟器，使用第一个可用设备
for /f "tokens=*" %%i in (temp_devices.txt) do (
    echo %%i | findstr /c:"[Empty]" >nul
    if errorlevel 1 (
        set "TABLET_DEVICE=%%i"
        goto :found_tablet
    )
)

:found_tablet
del temp_devices.txt 2>nul

if "%TABLET_DEVICE%"=="" (
    echo ❌ 错误：未找到可用的平板模拟器
    echo.
    echo 请按以下步骤启动平板模拟器：
    echo 1. 打开DevEco Studio
    echo 2. 点击 Tools ^> Device Manager
    echo 3. 创建或启动一个平板模拟器（推荐使用Tablet设备）
    echo 4. 等待模拟器完全启动后重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ 找到平板设备: %TABLET_DEVICE%

:: 查找HAP文件
echo [7/8] 查找HAP文件...
set "HAP_FILE="
for /r "entry\build\default\outputs\default" %%f in (*.hap) do (
    set "HAP_FILE=%%f"
    goto :found_hap
)

:found_hap
if "%HAP_FILE%"=="" (
    echo ❌ 错误：未找到HAP文件
    echo 请检查构建是否成功
    pause
    exit /b 1
)

echo ✅ HAP文件: %HAP_FILE%

:: 安装到平板模拟器
echo [8/8] 安装到平板模拟器...
echo 正在卸载旧版本...
hdc -t %TABLET_DEVICE% uninstall com.example.zuoye1 2>nul

echo 正在安装新版本...
hdc -t %TABLET_DEVICE% install "%HAP_FILE%"
if errorlevel 1 (
    echo ❌ 应用安装失败
    echo 请检查设备连接和权限设置
    pause
    exit /b 1
)

echo ✅ 应用安装成功

:: 启动应用
echo 正在启动应用...
hdc -t %TABLET_DEVICE% shell aa start -a EntryAbility -b com.example.zuoye1
if errorlevel 1 (
    echo ⚠️  应用启动可能失败，请手动在设备上启动应用
) else (
    echo ✅ 应用启动成功
)

echo.
echo ========================================
echo          🎉 平板部署完成！
echo ========================================
echo.
echo 📱 目标设备: %TABLET_DEVICE%
echo 📦 应用包名: com.example.zuoye1
echo 🚀 应用已在平板模拟器上运行
echo.
echo 💡 提示：
echo - 如果应用未自动启动，请在模拟器中手动点击应用图标
echo - 平板界面已针对大屏幕优化，享受更好的用户体验
echo - 如需重新部署，直接运行此脚本即可
echo.

pause
