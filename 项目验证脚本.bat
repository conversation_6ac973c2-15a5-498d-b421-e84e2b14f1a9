@echo off
echo ========================================
echo HarmonyOS音乐播放项目验证脚本
echo ========================================
echo.

echo 1. 检查项目结构...
if exist "entry\src\main\ets\pages\Index.ets" (
    echo ✅ 主页面文件存在
) else (
    echo ❌ 主页面文件缺失
)

if exist "entry\src\main\ets\services\MusicPlayerService.ets" (
    echo ✅ 音乐播放器服务存在
) else (
    echo ❌ 音乐播放器服务缺失
)

if exist "entry\src\main\ets\services\MusicDataService.ets" (
    echo ✅ 音乐数据服务存在
) else (
    echo ❌ 音乐数据服务缺失
)

if exist "entry\src\main\ets\common\MusicTypes.ets" (
    echo ✅ 音乐类型定义存在
) else (
    echo ❌ 音乐类型定义缺失
)

echo.
echo 2. 检查资源文件夹...
if exist "entry\src\main\resources\rawfile\music" (
    echo ✅ 音乐资源文件夹存在
) else (
    echo ❌ 音乐资源文件夹缺失
)

if exist "entry\src\main\resources\rawfile\music\covers" (
    echo ✅ 封面图片文件夹存在
) else (
    echo ❌ 封面图片文件夹缺失
)

echo.
echo 3. 检查配置文件...
if exist "entry\src\main\module.json5" (
    echo ✅ 模块配置文件存在
) else (
    echo ❌ 模块配置文件缺失
)

if exist "entry\src\main\resources\base\element\string.json" (
    echo ✅ 字符串资源文件存在
) else (
    echo ❌ 字符串资源文件缺失
)

echo.
echo 4. 音乐文件检查...
cd "entry\src\main\resources\rawfile\music" 2>nul
if %errorlevel% equ 0 (
    echo 音乐文件夹内容：
    dir /b *.mp3 2>nul
    if %errorlevel% equ 0 (
        echo ✅ 找到MP3文件
    ) else (
        echo ⚠️  未找到MP3文件，请添加音乐文件
    )
    cd ..\..\..\..\..\..
) else (
    echo ❌ 无法访问音乐文件夹
)

echo.
echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 📝 使用说明：
echo 1. 请将音乐文件（MP3格式）放入 entry\src\main\resources\rawfile\music\ 文件夹
echo 2. 请将封面图片放入 entry\src\main\resources\rawfile\music\covers\ 文件夹
echo 3. 在DevEco Studio中打开项目并编译运行
echo 4. 详细说明请查看"音乐播放功能使用说明.md"文件
echo.
pause
