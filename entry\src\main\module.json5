{
  // 模块配置信息
  "module": {
    "name": "entry",                                    // 模块名称
    "type": "entry",                                    // 模块类型：入口模块
    "description": "$string:module_desc",               // 模块描述（引用字符串资源）
    "mainElement": "EntryAbility",                      // 主要元素：入口Ability
    "deviceTypes": [                                    // 支持的设备类型
      "phone",                                          // 手机
      "tablet",                                         // 平板
      "2in1",                                           // 二合一设备
      "car"                                             // 车载设备
    ],
    "deliveryWithInstall": true,                        // 是否在安装时交付
    "installationFree": false,                          // 是否支持免安装
    "pages": "$profile:main_pages",                     // 页面配置文件引用
    "abilities": [                                      // Ability配置列表
      {
        "name": "EntryAbility",                         // Ability名称
        "srcEntry": "./ets/entryability/EntryAbility.ets", // 源文件路径
        "description": "$string:EntryAbility_desc",     // Ability描述
        "icon": "$media:layered_image",                 // Ability图标
        "label": "$string:EntryAbility_label",          // Ability标签
        "startWindowIcon": "$media:startIcon",          // 启动窗口图标
        "startWindowBackground": "$color:start_window_background", // 启动窗口背景色
        "exported": true,                               // 是否可被其他应用调用
        "skills": [                                     // 技能配置（Intent过滤器）
          {
            "entities": [                               // 实体类型
              "entity.system.home"                      // 系统主页实体
            ],
            "actions": [                                // 动作类型
              "action.system.home"                      // 系统主页动作
            ]
          }
        ]
      }
    ],
    "extensionAbilities": [                             // 扩展能力配置列表
      {
        "name": "EntryBackupAbility",                   // 扩展能力名称
        "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", // 源文件路径
        "type": "backup",                               // 扩展能力类型：备份
        "exported": false,                              // 不对外暴露
        "metadata": [                                   // 元数据配置
          {
            "name": "ohos.extension.backup",            // 备份扩展标识
            "resource": "$profile:backup_config"        // 备份配置文件引用
          }
        ],
      }
    ],
    "requestPermissions": [                             // 权限申请配置
      {
        "name": "ohos.permission.READ_MEDIA",           // 读取媒体文件权限
        "reason": "$string:permission_read_media",      // 权限申请理由
        "usedScene": {                                  // 使用场景
          "abilities": ["EntryAbility"],                // 使用该权限的Ability
          "when": "inuse"                               // 使用时机：使用时
        }
      },
      {
        "name": "ohos.permission.WRITE_MEDIA",          // 写入媒体文件权限
        "reason": "$string:permission_write_media",     // 权限申请理由
        "usedScene": {                                  // 使用场景
          "abilities": ["EntryAbility"],                // 使用该权限的Ability
          "when": "inuse"                               // 使用时机：使用时
        }
      },
      {
        "name": "ohos.permission.MICROPHONE",           // 麦克风权限（音频录制相关）
        "reason": "$string:permission_microphone",      // 权限申请理由
        "usedScene": {                                  // 使用场景
          "abilities": ["EntryAbility"],                // 使用该权限的Ability
          "when": "inuse"                               // 使用时机：使用时
        }
      },
      {
        "name": "ohos.permission.INTERNET",             // 网络访问权限
        "reason": "$string:permission_internet",        // 权限申请理由
        "usedScene": {                                  // 使用场景
          "abilities": ["EntryAbility"],                // 使用该权限的Ability
          "when": "inuse"                               // 使用时机：使用时
        }
      }
    ]
  }
}