@echo off
echo Fixing HarmonyOS build issues...

echo Step 1: Kill all node processes
taskkill /f /im node.exe >nul 2>&1

echo Step 2: Clean cache
if exist "C:\Users\<USER>\.hvigor\daemon\cache\daemon-sec.json" (
    del "C:\Users\<USER>\.hvigor\daemon\cache\daemon-sec.json" >nul 2>&1
)

if exist ".preview" (
    rmdir /s /q ".preview" >nul 2>&1
)

echo Step 3: Set SDK environment
set DEVECO_SDK_HOME=D:\DevEco Studio\sdk

echo Step 4: Build with no-daemon
"D:\DevEco Studio\tools\node\node.exe" "D:\DevEco Studio\tools\hvigor\bin\hvigorw.js" --mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --no-daemon --analyze=normal --parallel --incremental

echo Build completed!
pause
