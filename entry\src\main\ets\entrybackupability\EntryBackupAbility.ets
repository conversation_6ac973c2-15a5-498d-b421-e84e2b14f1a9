// 导入性能分析和文件备份相关模块
import { hilog } from '@kit.PerformanceAnalysisKit';
import { BackupExtensionAbility, BundleVersion } from '@kit.CoreFileKit';

// 定义日志域标识符，用于日志输出的分类
const DOMAIN = 0x0000;

/**
 * 应用数据备份恢复扩展能力类
 * 继承自BackupExtensionAbility，提供应用数据的备份和恢复功能
 */
export default class EntryBackupAbility extends BackupExtensionAbility {
  /**
   * 执行数据备份操作的异步方法
   * 当系统需要备份应用数据时会调用此方法
   * @returns Promise<void> - 返回Promise表示异步操作完成
   */
  async onBackup() {
    // 记录备份操作开始的日志
    hilog.info(DOMAIN, 'testTag', 'onBackup ok');
    // 等待异步操作完成（这里是空操作，实际项目中应包含具体的备份逻辑）
    await Promise.resolve();
  }

  /**
   * 执行数据恢复操作的异步方法
   * 当系统需要恢复应用数据时会调用此方法
   * @param bundleVersion - 包版本信息，包含应用的版本详情
   * @returns Promise<void> - 返回Promise表示异步操作完成
   */
  async onRestore(bundleVersion: BundleVersion) {
    // 记录恢复操作开始的日志，并输出版本信息
    hilog.info(DOMAIN, 'testTag', 'onRestore ok %{public}s', JSON.stringify(bundleVersion));
    // 等待异步操作完成（这里是空操作，实际项目中应包含具体的恢复逻辑）
    await Promise.resolve();
  }
}