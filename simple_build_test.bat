@echo off
echo Testing HarmonyOS project build...
echo.

echo Checking if hvigorw exists...
if exist "hvigorw.bat" (
    echo Found hvigorw.bat, attempting build...
    call hvigorw.bat assembleHap --mode module -p product=default -p buildMode=debug
) else (
    echo hvigorw.bat not found, trying alternative methods...
    
    if exist "node_modules\.bin\hvigor.cmd" (
        echo Found hvigor in node_modules...
        call node_modules\.bin\hvigor.cmd assembleHap --mode module -p product=default -p buildMode=debug
    ) else (
        echo No build tools found. Please use DevEco Studio to build the project.
        echo.
        echo Steps:
        echo 1. Open DevEco Studio
        echo 2. Open this project
        echo 3. Click Build - Make Project
        echo.
    )
)

echo.
echo Build test completed.
pause
