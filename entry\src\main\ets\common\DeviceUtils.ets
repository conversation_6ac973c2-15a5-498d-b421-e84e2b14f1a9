/**
 * 设备工具类
 * 用于检测设备类型和屏幕信息，提供设备适配功能
 */

import { display } from '@kit.ArkUI';
import { deviceInfo } from '@kit.BasicServicesKit';

/**
 * 设备类型枚举
 */
export enum DeviceType {
  PHONE = 'phone',
  TABLET = 'tablet',
  CAR = 'car',
  TV = 'tv',
  WEARABLE = 'wearable',
  UNKNOWN = 'unknown'
}

/**
 * 屏幕尺寸分类
 */
export enum ScreenSize {
  SMALL = 'small',    // 小屏幕 (< 600dp)
  MEDIUM = 'medium',  // 中等屏幕 (600dp - 840dp)
  LARGE = 'large',    // 大屏幕 (> 840dp)
  XLARGE = 'xlarge'   // 超大屏幕 (> 1200dp)
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  deviceType: DeviceType;
  screenSize: ScreenSize;
  screenWidth: number;
  screenHeight: number;
  screenDensity: number;
  isTablet: boolean;
  isPhone: boolean;
  isCar: boolean;
}

/**
 * 设备工具类
 */
export class DeviceUtils {
  private static instance: DeviceUtils;
  private deviceInfo: DeviceInfo | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): DeviceUtils {
    if (!DeviceUtils.instance) {
      DeviceUtils.instance = new DeviceUtils();
    }
    return DeviceUtils.instance;
  }

  /**
   * 初始化设备信息
   */
  public async initialize(): Promise<void> {
    try {
      // 获取屏幕信息
      const displayInfo = await display.getDefaultDisplaySync();
      
      // 获取设备类型信息
      const deviceTypeInfo = deviceInfo.deviceType;
      
      // 计算屏幕尺寸分类
      const screenWidthDp = this.pxToDp(displayInfo.width, displayInfo.densityDPI);
      const screenSize = this.calculateScreenSize(screenWidthDp);
      
      // 确定设备类型
      const deviceType = this.determineDeviceType(deviceTypeInfo, screenSize, screenWidthDp);
      
      this.deviceInfo = {
        deviceType: deviceType,
        screenSize: screenSize,
        screenWidth: displayInfo.width,
        screenHeight: displayInfo.height,
        screenDensity: displayInfo.densityDPI,
        isTablet: deviceType === DeviceType.TABLET,
        isPhone: deviceType === DeviceType.PHONE,
        isCar: deviceType === DeviceType.CAR
      };

      console.info('设备信息初始化完成:', JSON.stringify(this.deviceInfo));
    } catch (error) {
      console.error('设备信息初始化失败:', error);
      // 设置默认值
      this.deviceInfo = {
        deviceType: DeviceType.UNKNOWN,
        screenSize: ScreenSize.MEDIUM,
        screenWidth: 720,
        screenHeight: 1280,
        screenDensity: 320,
        isTablet: false,
        isPhone: true,
        isCar: false
      };
    }
  }

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): DeviceInfo {
    if (!this.deviceInfo) {
      throw new Error('设备信息未初始化，请先调用initialize()方法');
    }
    return this.deviceInfo;
  }

  /**
   * 判断是否为平板设备
   */
  public isTablet(): boolean {
    return this.getDeviceInfo().isTablet;
  }

  /**
   * 判断是否为手机设备
   */
  public isPhone(): boolean {
    return this.getDeviceInfo().isPhone;
  }

  /**
   * 判断是否为车载设备
   */
  public isCar(): boolean {
    return this.getDeviceInfo().isCar;
  }

  /**
   * 获取屏幕尺寸分类
   */
  public getScreenSize(): ScreenSize {
    return this.getDeviceInfo().screenSize;
  }

  /**
   * 判断是否为大屏设备（平板或更大）
   */
  public isLargeScreen(): boolean {
    const screenSize = this.getScreenSize();
    return screenSize === ScreenSize.LARGE || screenSize === ScreenSize.XLARGE;
  }

  /**
   * 像素转换为dp
   */
  private pxToDp(px: number, density: number): number {
    return px / (density / 160);
  }

  /**
   * 根据屏幕宽度计算屏幕尺寸分类
   */
  private calculateScreenSize(screenWidthDp: number): ScreenSize {
    if (screenWidthDp < 600) {
      return ScreenSize.SMALL;
    } else if (screenWidthDp < 840) {
      return ScreenSize.MEDIUM;
    } else if (screenWidthDp < 1200) {
      return ScreenSize.LARGE;
    } else {
      return ScreenSize.XLARGE;
    }
  }

  /**
   * 确定设备类型
   */
  private determineDeviceType(systemDeviceType: string, screenSize: ScreenSize, screenWidthDp: number): DeviceType {
    // 首先检查系统报告的设备类型
    const lowerDeviceType = systemDeviceType.toLowerCase();
    
    if (lowerDeviceType.includes('tablet') || lowerDeviceType.includes('pad')) {
      return DeviceType.TABLET;
    }
    
    if (lowerDeviceType.includes('car') || lowerDeviceType.includes('vehicle')) {
      return DeviceType.CAR;
    }
    
    if (lowerDeviceType.includes('tv') || lowerDeviceType.includes('television')) {
      return DeviceType.TV;
    }
    
    if (lowerDeviceType.includes('watch') || lowerDeviceType.includes('wearable')) {
      return DeviceType.WEARABLE;
    }
    
    // 根据屏幕尺寸推断设备类型
    if (screenSize === ScreenSize.LARGE || screenSize === ScreenSize.XLARGE) {
      // 大屏幕通常是平板
      return DeviceType.TABLET;
    } else if (screenSize === ScreenSize.SMALL || screenSize === ScreenSize.MEDIUM) {
      // 小屏幕通常是手机
      return DeviceType.PHONE;
    }
    
    // 默认返回手机类型
    return DeviceType.PHONE;
  }

  /**
   * 获取适配的字体大小
   */
  public getAdaptiveFontSize(baseSize: number): number {
    const deviceInfo = this.getDeviceInfo();
    
    if (deviceInfo.isTablet) {
      // 平板字体放大1.3倍
      return Math.round(baseSize * 1.3);
    } else if (deviceInfo.screenSize === ScreenSize.XLARGE) {
      // 超大屏幕字体放大1.5倍
      return Math.round(baseSize * 1.5);
    }
    
    return baseSize;
  }

  /**
   * 获取适配的尺寸
   */
  public getAdaptiveSize(baseSize: number): number {
    const deviceInfo = this.getDeviceInfo();
    
    if (deviceInfo.isTablet) {
      // 平板尺寸放大1.4倍
      return Math.round(baseSize * 1.4);
    } else if (deviceInfo.screenSize === ScreenSize.XLARGE) {
      // 超大屏幕尺寸放大1.6倍
      return Math.round(baseSize * 1.6);
    }
    
    return baseSize;
  }

  /**
   * 获取适配的间距
   */
  public getAdaptiveSpacing(baseSpacing: number): number {
    const deviceInfo = this.getDeviceInfo();
    
    if (deviceInfo.isTablet) {
      // 平板间距放大1.5倍
      return Math.round(baseSpacing * 1.5);
    } else if (deviceInfo.screenSize === ScreenSize.XLARGE) {
      // 超大屏幕间距放大1.8倍
      return Math.round(baseSpacing * 1.8);
    }
    
    return baseSpacing;
  }

  /**
   * 打印设备信息（调试用）
   */
  public printDeviceInfo(): void {
    if (this.deviceInfo) {
      console.info('=== 设备信息 ===');
      console.info(`设备类型: ${this.deviceInfo.deviceType}`);
      console.info(`屏幕尺寸分类: ${this.deviceInfo.screenSize}`);
      console.info(`屏幕宽度: ${this.deviceInfo.screenWidth}px`);
      console.info(`屏幕高度: ${this.deviceInfo.screenHeight}px`);
      console.info(`屏幕密度: ${this.deviceInfo.screenDensity}dpi`);
      console.info(`是否平板: ${this.deviceInfo.isTablet}`);
      console.info(`是否手机: ${this.deviceInfo.isPhone}`);
      console.info(`是否车载: ${this.deviceInfo.isCar}`);
      console.info('===============');
    }
  }
}
