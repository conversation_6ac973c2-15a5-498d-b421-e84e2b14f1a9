# 音乐文件放置说明

## 📁 文件夹结构

```
entry/src/main/resources/rawfile/music/
├── README.md                    # 本说明文件
├── covers/                      # 音乐封面图片文件夹
│   ├── hakimi.jpg              # 燃尽的哈基米封面
│   ├── example.jpg             # 示例音乐封面
│   ├── brightest_star.jpg      # 夜空中最亮的星封面
│   ├── chengdu.jpg             # 成都封面
│   └── confession_balloon.jpg  # 告白气球封面
└── 音乐文件 (MP3格式)
    ├── hakimi.mp3              # 燃尽的哈基米
    ├── example.mp3             # 车载音乐示例
    ├── brightest_star.mp3      # 夜空中最亮的星
    ├── chengdu.mp3             # 成都
    └── confession_balloon.mp3  # 告白气球
```

## 🎵 如何添加音乐文件

### 1. 音乐文件要求
- **格式**: MP3 (推荐)、AAC、FLAC等HarmonyOS支持的音频格式
- **文件名**: 使用英文命名，避免特殊字符和空格
- **大小**: 建议单个文件不超过50MB
- **质量**: 建议128kbps以上，320kbps以下

### 2. 封面图片要求
- **格式**: JPG、PNG
- **尺寸**: 建议300x300像素或500x500像素
- **文件名**: 与对应音乐文件同名（扩展名不同）

### 3. 添加步骤

#### 方法一：直接复制文件
1. 将MP3音乐文件复制到 `entry/src/main/resources/rawfile/music/` 目录
2. 将对应的封面图片复制到 `entry/src/main/resources/rawfile/music/covers/` 目录
3. 在 `MusicDataService.ets` 中的 `getDefaultMusicList()` 方法里添加新的音乐信息

#### 方法二：使用开发工具
1. 在DevEco Studio中右键点击 `rawfile/music` 文件夹
2. 选择 "New" -> "File" 或直接拖拽文件到文件夹中
3. 确保文件被正确添加到项目中

### 4. 更新代码中的音乐列表

在 `entry/src/main/ets/services/MusicDataService.ets` 文件中找到 `getDefaultMusicList()` 方法，按以下格式添加新音乐：

```typescript
{
  id: '6',                                    // 唯一ID
  title: '你的音乐标题',                        // 歌曲名称
  artist: '艺术家名称',                        // 艺术家
  album: '专辑名称',                          // 专辑（可选）
  duration: 240000,                          // 时长（毫秒）
  uri: 'resource://RAWFILE/music/your_music.mp3',  // 音乐文件路径
  coverUri: 'resource://RAWFILE/music/covers/your_music.jpg', // 封面路径
  isLiked: false,                            // 是否点赞
  isFavorite: false,                         // 是否收藏
  playCount: 0                               // 播放次数
}
```

## 🎼 示例音乐文件

项目中预设了以下示例音乐（需要您自行添加实际的音乐文件）：

1. **燃尽的哈基米** - hakimi.mp3
2. **车载音乐示例** - example.mp3  
3. **夜空中最亮的星** - brightest_star.mp3
4. **成都** - chengdu.mp3
5. **告白气球** - confession_balloon.mp3

## ⚠️ 注意事项

1. **版权问题**: 请确保您有权使用添加的音乐文件，避免版权纠纷
2. **文件大小**: 过大的音乐文件会增加应用包体积
3. **文件路径**: 确保文件路径在代码中正确配置
4. **编译问题**: 添加文件后需要重新编译项目
5. **测试**: 添加新音乐后请在设备上测试播放功能

## 🔧 故障排除

### 音乐无法播放
1. 检查文件格式是否支持
2. 确认文件路径是否正确
3. 检查权限配置是否正确
4. 查看控制台错误日志

### 封面不显示
1. 确认图片文件存在
2. 检查图片格式是否支持
3. 确认文件路径配置正确

### 应用无法启动
1. 检查是否有语法错误
2. 确认所有引用的文件都存在
3. 重新清理并编译项目

## 📞 技术支持

如果遇到问题，请检查：
1. HarmonyOS开发文档中的媒体播放相关章节
2. 项目中的错误日志
3. DevEco Studio的编译输出信息
