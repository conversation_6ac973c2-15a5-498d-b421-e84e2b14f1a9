/**
 * 音频测试工具类
 * 用于诊断音频播放问题
 */

import media from '@ohos.multimedia.media';

export class AudioTestUtil {
  private static avPlayer: media.AVPlayer | null = null;

  /**
   * 测试音频文件是否可以播放
   */
  public static async testAudioFile(filePath: string): Promise<boolean> {
    try {
      console.info('🧪 开始测试音频文件:', filePath);
      
      // 创建播放器
      if (this.avPlayer) {
        await this.avPlayer.release();
      }
      
      this.avPlayer = await media.createAVPlayer();
      
      // 设置音量
      this.avPlayer.volume = 1.0;
      console.info('🧪 设置音量为:', this.avPlayer.volume);
      
      // 设置回调
      this.avPlayer.on('stateChange', (state) => {
        console.info('🧪 播放器状态:', state);
      });
      
      this.avPlayer.on('error', (error) => {
        console.error('🧪 播放器错误:', error);
      });
      
      this.avPlayer.on('durationUpdate', (duration) => {
        console.info('🧪 音频时长:', duration, 'ms');
      });
      
      // 设置音频文件
      this.avPlayer.url = filePath;
      console.info('🧪 设置音频URL:', filePath);
      
      // 准备播放
      await this.avPlayer.prepare();
      console.info('🧪 音频准备完成');
      
      // 尝试播放
      await this.avPlayer.play();
      console.info('🧪 开始播放');
      
      // 等待一秒后暂停
      setTimeout(async () => {
        if (this.avPlayer) {
          await this.avPlayer.pause();
          console.info('🧪 暂停播放');
        }
      }, 1000);
      
      return true;
    } catch (error) {
      console.error('🧪 音频测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试所有音频文件
   */
  public static async testAllAudioFiles(): Promise<void> {
    const audioFiles = [
      'resource://RAWFILE/music/song1.mp3',
      'resource://RAWFILE/music/song2.mp3', 
      'resource://RAWFILE/music/song3.mp3',
      'resource://RAWFILE/music/song4.mp3',
      'resource://RAWFILE/music/song5.mp3'
    ];
    
    console.info('🧪 开始测试所有音频文件...');
    
    for (let i = 0; i < audioFiles.length; i++) {
      const filePath = audioFiles[i];
      console.info(`🧪 测试文件 ${i + 1}/${audioFiles.length}:`, filePath);
      
      const result = await this.testAudioFile(filePath);
      console.info(`🧪 文件 ${i + 1} 测试结果:`, result ? '✅ 成功' : '❌ 失败');
      
      // 等待一秒再测试下一个
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.info('🧪 所有音频文件测试完成');
  }
  
  /**
   * 释放资源
   */
  public static async release(): Promise<void> {
    if (this.avPlayer) {
      try {
        await this.avPlayer.release();
        this.avPlayer = null;
        console.info('🧪 音频测试工具资源释放完成');
      } catch (error) {
        console.error('🧪 释放音频测试工具失败:', error);
      }
    }
  }
  
  /**
   * 获取系统音频信息
   */
  public static async getSystemAudioInfo(): Promise<void> {
    try {
      console.info('🧪 获取系统音频信息...');
      
      // 创建临时播放器获取信息
      const tempPlayer = await media.createAVPlayer();
      
      console.info('🧪 播放器创建成功');
      console.info('🧪 播放器状态:', tempPlayer.state);
      console.info('🧪 播放器音量:', tempPlayer.volume);
      
      await tempPlayer.release();
      console.info('🧪 系统音频信息获取完成');
    } catch (error) {
      console.error('🧪 获取系统音频信息失败:', error);
    }
  }
}
