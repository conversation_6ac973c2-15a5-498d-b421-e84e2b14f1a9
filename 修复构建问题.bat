@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 修复HarmonyOS构建问题
echo ========================================
echo.

echo [1/5] 终止所有Node.js进程...
taskkill /f /im node.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js进程已终止
) else (
    echo ℹ️ 没有运行的Node.js进程
)

echo.
echo [2/5] 清理构建缓存...
if exist "C:\Users\<USER>\.hvigor\daemon\cache\daemon-sec.json" (
    del "C:\Users\<USER>\.hvigor\daemon\cache\daemon-sec.json" >nul 2>&1
    echo ✅ 已删除daemon缓存文件
) else (
    echo ℹ️ daemon缓存文件不存在
)

if exist ".preview" (
    rmdir /s /q ".preview" >nul 2>&1
    echo ✅ 已清理.preview文件夹
)

if exist "entry\build" (
    rmdir /s /q "entry\build" >nul 2>&1
    echo ✅ 已清理entry\build文件夹
)

echo.
echo [3/5] 检查项目文件完整性...
if exist "entry\src\main\ets\pages\Index.ets" (
    echo ✅ Index.ets 存在
) else (
    echo ❌ Index.ets 缺失
)

if exist "entry\src\main\ets\pages\MusicPage.ets" (
    echo ✅ MusicPage.ets 存在
) else (
    echo ❌ MusicPage.ets 缺失
)

if exist "entry\src\main\resources\base\profile\main_pages.json" (
    echo ✅ main_pages.json 存在
) else (
    echo ❌ main_pages.json 缺失
)

echo.
echo [4/5] 使用--no-daemon参数重新构建...
echo 正在启动构建...

"D:\DevEco Studio\tools\node\node.exe" "D:\DevEco Studio\tools\hvigor\bin\hvigorw.js" --mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --no-daemon --analyze=normal --parallel --incremental

echo.
echo [5/5] 构建完成检查...
if exist ".preview" (
    echo ✅ 预览构建成功
) else (
    echo ❌ 预览构建失败
)

echo.
echo ========================================
echo 🎯 修复完成！
echo ========================================
echo.
echo 💡 如果仍有问题，请尝试：
echo 1. 重启DevEco Studio
echo 2. 清理项目缓存
echo 3. 重新同步项目
echo.
pause
