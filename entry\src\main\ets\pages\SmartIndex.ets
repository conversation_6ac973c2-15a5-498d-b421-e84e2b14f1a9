// 导入设备工具类
import { DeviceUtils, DeviceType } from '../common/DeviceUtils';
// 导入系统日期时间模块
import systemDateTime from '@ohos.systemDateTime';
// 导入音乐播放相关模块
import { MusicPlayerService } from '../services/MusicPlayerService';
import { MusicDataService } from '../services/MusicDataService';
import { MusicInfo, PlayState, PlayMode, PlayerEvent } from '../common/MusicTypes';
import { common } from '@kit.AbilityKit';

/**
 * 智能入口页面组件
 * 根据设备类型自动选择合适的UI界面
 * @Entry 表示这是应用的入口页面
 * @Component 表示这是一个自定义组件
 */
@Entry
@Component
struct SmartIndex {
  // 设备检测相关状态
  @State isDeviceReady: boolean = false;
  @State isTabletDevice: boolean = false;
  @State deviceType: string = 'unknown';
  @State errorMessage: string = '';

  // 设备工具实例
  private deviceUtils: DeviceUtils = DeviceUtils.getInstance();

  /**
   * 页面即将出现时的回调函数
   * 在页面显示前执行设备检测和初始化
   */
  async aboutToAppear(): Promise<void> {
    console.info('SmartIndex页面即将出现，开始设备检测');
    
    try {
      // 初始化设备工具
      await this.deviceUtils.initialize();
      
      // 获取设备信息
      const deviceInfo = this.deviceUtils.getDeviceInfo();
      
      // 更新状态
      this.isTabletDevice = deviceInfo.isTablet;
      this.deviceType = deviceInfo.deviceType;
      this.isDeviceReady = true;
      
      // 打印设备信息（调试用）
      this.deviceUtils.printDeviceInfo();
      
      console.info(`设备检测完成: ${this.deviceType}, 是否平板: ${this.isTabletDevice}`);
    } catch (error) {
      console.error('设备检测失败:', error);
      this.errorMessage = '设备检测失败，使用默认界面';
      this.isDeviceReady = true; // 即使失败也要显示界面
    }
  }

  /**
   * 构建加载界面
   */
  @Builder
  buildLoadingScreen() {
    Column() {
      // 加载动画
      LoadingProgress()
        .width(60)
        .height(60)
        .color('#007DFF')
        .margin({ bottom: 20 })

      Text('正在检测设备类型...')
        .fontSize(18)
        .fontColor(Color.White)
        .margin({ bottom: 10 })

      Text('请稍候')
        .fontSize(14)
        .fontColor('#CCCCCC')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 构建错误界面
   */
  @Builder
  buildErrorScreen() {
    Column() {
      Image($r('app.media.xin'))
        .width(80)
        .height(80)
        .margin({ bottom: 20 })

      Text('设备检测异常')
        .fontSize(20)
        .fontColor('#FF6B6B')
        .margin({ bottom: 10 })

      Text(this.errorMessage)
        .fontSize(14)
        .fontColor('#CCCCCC')
        .textAlign(TextAlign.Center)
        .margin({ bottom: 20 })

      Button('使用默认界面')
        .fontSize(16)
        .backgroundColor('#007DFF')
        .borderRadius(8)
        .onClick(() => {
          this.isTabletDevice = false; // 强制使用手机界面
        })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .padding(40)
  }

  /**
   * 构建设备信息显示（调试用）
   */
  @Builder
  buildDeviceInfo() {
    if (this.deviceUtils && this.isDeviceReady) {
      // const deviceInfo = this.deviceUtils.getDeviceInfo();

      Column() {
        Text(`设备类型: 调试模式`)
          .fontSize(12)
          .fontColor('#666666')

        Text(`屏幕: 调试信息`)
          .fontSize(12)
          .fontColor('#666666')

        Text(`密度: 调试信息`)
          .fontSize(12)
          .fontColor('#666666')
      }
      .position({ top: 10, right: 10 })
      .padding(8)
      .backgroundColor('#1a1a1a')
      .borderRadius(4)
      .zIndex(1000)
    }
  }

  /**
   * 主构建函数
   */
  build() {
    Stack() {
      // 根据设备检测结果显示不同界面
      if (!this.isDeviceReady) {
        // 显示加载界面
        this.buildLoadingScreen()
      } else if (this.errorMessage) {
        // 显示错误界面
        this.buildErrorScreen()
      } else {
        // 根据设备类型选择合适的界面
        if (this.isTabletDevice) {
          // 平板设备使用平板优化界面
          IndexTablet()
        } else {
          // 其他设备使用标准界面
          StandardIndex()
        }
      }

      // 调试信息（仅在开发模式显示）
      // if (__DEV__) {
      //   this.buildDeviceInfo()
      // }
    }
    .width('100%')
    .height('100%')
  }
}

/**
 * 标准界面组件（手机/车载等设备）
 */
@Component
struct StandardIndex {
  build() {
    Column() {
      Text('标准界面')
        .fontSize(24)
        .fontColor(Color.White)
        .textAlign(TextAlign.Center)

      Text('适用于手机和车载设备')
        .fontSize(16)
        .fontColor('#CCCCCC')
        .textAlign(TextAlign.Center)
        .margin({ top: 20 })

      Text('使用原始Index组件功能')
        .fontSize(14)
        .fontColor('#007DFF')
        .textAlign(TextAlign.Center)
        .margin({ top: 40 })

      Text('请切换到平板模拟器查看优化界面')
        .fontSize(12)
        .fontColor('#999999')
        .textAlign(TextAlign.Center)
        .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }
}

/**
 * 平板界面组件
 */
@Component
struct IndexTablet {
  /**
   * 自定义构建器函数，用于创建带图标的TabBar（平板优化）
   * @param tupian - 图片资源路径字符串
   * @returns 返回配置好的Image组件
   */
  @Builder imageBuilder(tupian: string) {
    Image($r(tupian))
      .width(60)  // 平板图标加大
      .height(60)
  }

  // 状态变量：左侧滑块的数值（温度控制）
  @State sliderValue: number = 30;
  // 状态变量：右侧滑块的数值（温度控制）
  @State sliderValue1: number = 30;
  // 状态变量：当前时间字符串显示
  @State timeStr: string = "加载中...";
  // 私有变量：定时器ID，用于定时更新时间
  private timer: number = 0;

  // 音乐播放相关状态变量
  @State currentMusic: MusicInfo | null = null;        // 当前播放的音乐
  @State playState: PlayState = PlayState.STOPPED;     // 播放状态
  @State playMode: PlayMode = PlayMode.SEQUENCE;       // 播放模式
  @State currentPosition: number = 0;                  // 当前播放位置（毫秒）
  @State duration: number = 0;                         // 总时长（毫秒）
  @State volume: number = 50;                          // 音量
  @State isLoading: boolean = false;                   // 是否正在加载
  @State playlist: MusicInfo[] = [];                   // 播放列表
  @State isLiked: boolean = false;                     // 当前歌曲是否点赞
  @State isFavorite: boolean = false;                  // 当前歌曲是否收藏

  // 音乐服务实例
  private musicPlayer: MusicPlayerService = new MusicPlayerService();
  private musicDataService: MusicDataService = new MusicDataService();

  /**
   * 页面即将出现时的回调函数
   */
  async aboutToAppear(): Promise<void> {
    console.info('IndexTablet页面即将出现，开始初始化');

    try {
      // 启动定时器更新时间显示
      this.startTimeUpdate();

      // 初始化音乐播放器
      await this.initializeMusicPlayer();

      console.info('IndexTablet页面初始化完成');
    } catch (error) {
      console.error('IndexTablet页面初始化失败:', error);
    }
  }

  /**
   * 页面即将消失时的回调函数
   */
  async aboutToDisappear(): Promise<void> {
    console.info('IndexTablet页面即将消失，开始清理资源');

    try {
      // 停止定时器
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = 0;
      }

      // 释放音乐播放器资源
      await this.releaseMusicPlayer();

      console.info('IndexTablet页面资源清理完成');
    } catch (error) {
      console.error('IndexTablet页面资源清理失败:', error);
    }
  }

  /**
   * 启动时间更新定时器
   */
  private startTimeUpdate(): void {
    // 立即更新一次时间
    this.updateTime();

    // 设置定时器，每1000毫秒（1秒）更新一次时间
    this.timer = setInterval(() => {
      this.updateTime();
    }, 1000);
  }

  /**
   * 更新时间显示
   */
  private async updateTime(): Promise<void> {
    try {
      // 获取当前系统时间戳（毫秒）
      const currentTime = await systemDateTime.getTime();
      // 创建Date对象
      const date = new Date(currentTime);
      // 格式化时间字符串：小时:分钟
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      this.timeStr = `${hours}:${minutes}`;
    } catch (error) {
      console.error('获取系统时间失败:', error);
      this.timeStr = "时间获取失败";
    }
  }

  /**
   * 初始化音乐播放器
   */
  private async initializeMusicPlayer(): Promise<void> {
    try {
      console.info('开始初始化音乐播放器...');

      // 初始化音乐数据服务
      await this.musicDataService.initialize();

      // 获取播放列表
      this.playlist = await this.musicDataService.getAllMusic();
      console.info(`加载了 ${this.playlist.length} 首音乐`);

      // 设置当前播放音乐（如果有的话）
      if (this.playlist.length > 0) {
        this.currentMusic = this.playlist[0];
        await this.updateCurrentMusicState();
      }

      // 初始化音乐播放器
      await this.musicPlayer.initialize();

      // 设置播放列表
      this.musicPlayer.setPlaylist(this.playlist);

      // 注册播放器事件监听
      this.registerPlayerEvents();

      // 恢复播放状态
      await this.restorePlayState();

      console.info('音乐播放器初始化完成');
    } catch (error) {
      console.error('音乐播放器初始化失败:', error);
    }
  }

  /**
   * 注册播放器事件监听
   */
  private registerPlayerEvents(): void {
    // 播放状态变化事件
    this.musicPlayer.on(PlayerEvent.STATE_CHANGED, (state: PlayState) => {
      this.playState = state;
      console.info(`播放状态变化: ${state}`);
    });

    // 音乐切换事件
    this.musicPlayer.on(PlayerEvent.MUSIC_CHANGED, async (music: MusicInfo) => {
      this.currentMusic = music;
      await this.updateCurrentMusicState();
      console.info(`音乐切换: ${music.title}`);
    });

    // 播放进度更新事件
    this.musicPlayer.on(PlayerEvent.POSITION_CHANGED, (position: number) => {
      this.currentPosition = position;
    });

    // 播放时长更新事件
    this.musicPlayer.on(PlayerEvent.DURATION_CHANGED, (duration: number) => {
      this.duration = duration;
    });

    // 播放模式变化事件
    this.musicPlayer.on(PlayerEvent.PLAY_MODE_CHANGED, (mode: PlayMode) => {
      this.playMode = mode;
      console.info(`播放模式变化: ${mode}`);
    });

    // 音量变化事件
    this.musicPlayer.on(PlayerEvent.VOLUME_CHANGED, (volume: number) => {
      this.volume = volume * 100;
    });

    // 加载状态变化事件
    this.musicPlayer.on(PlayerEvent.LOADING_CHANGED, (loading: boolean) => {
      this.isLoading = loading;
    });

    // 错误事件
    this.musicPlayer.on(PlayerEvent.ERROR, (error: string) => {
      console.error('播放器错误:', error);
    });
  }

  /**
   * 恢复播放状态
   */
  private async restorePlayState(): Promise<void> {
    try {
      // 恢复播放模式
      const savedPlayMode: PlayMode = this.musicDataService.getPlayMode();
      if (savedPlayMode) {
        this.playMode = savedPlayMode;
        this.musicPlayer.setPlayMode(savedPlayMode);
      }

      // 恢复音量
      const savedVolume: number = this.musicDataService.getVolume();
      if (savedVolume !== null) {
        this.volume = savedVolume * 100;
        this.musicPlayer.setVolume(savedVolume);
      }

      console.info('播放状态恢复完成');
    } catch (error) {
      console.error('恢复播放状态失败:', error);
    }
  }

  /**
   * 更新当前音乐的状态信息
   */
  private async updateCurrentMusicState(): Promise<void> {
    if (!this.currentMusic) return;

    try {
      // 更新点赞和收藏状态
      this.isLiked = this.musicDataService.isLiked(this.currentMusic.id);
      this.isFavorite = this.musicDataService.isFavorite(this.currentMusic.id);
    } catch (error) {
      console.error('更新音乐状态失败:', error);
      this.isLiked = false;
      this.isFavorite = false;
    }
  }

  /**
   * 释放音乐播放器资源
   */
  private async releaseMusicPlayer(): Promise<void> {
    try {
      await this.musicPlayer.release();
      await this.musicDataService.release();
      console.info('音乐播放器资源释放完成');
    } catch (error) {
      console.error('释放音乐播放器资源失败:', error);
    }
  }

  // ==================== 音乐控制方法 ====================

  /**
   * 播放/暂停切换
   */
  private async onPlayPauseClick(): Promise<void> {
    try {
      if (this.playState === PlayState.PLAYING) {
        await this.musicPlayer.pause();
      } else if (this.playState === PlayState.PAUSED) {
        await this.musicPlayer.play();
      } else {
        // 如果是停止状态，开始播放当前歌曲
        if (this.currentMusic) {
          await this.musicPlayer.loadMusic(this.currentMusic);
          await this.musicPlayer.play();
          // 添加到最近播放
          await this.musicDataService.addToRecentPlay(this.currentMusic.id);
        }
      }
    } catch (error) {
      console.error('播放/暂停操作失败:', error);
    }
  }

  /**
   * 上一首
   */
  private async onPreviousClick(): Promise<void> {
    try {
      await this.musicPlayer.previous();
      if (this.currentMusic) {
        await this.musicDataService.addToRecentPlay(this.currentMusic.id);
      }
    } catch (error) {
      console.error('上一首操作失败:', error);
    }
  }

  /**
   * 下一首
   */
  private async onNextClick(): Promise<void> {
    try {
      await this.musicPlayer.next();
      if (this.currentMusic) {
        await this.musicDataService.addToRecentPlay(this.currentMusic.id);
      }
    } catch (error) {
      console.error('下一首操作失败:', error);
    }
  }

  /**
   * 点赞/取消点赞
   */
  private async onLikeClick(): Promise<void> {
    if (!this.currentMusic) return;

    try {
      if (this.isLiked) {
        await this.musicDataService.unlikeSong(this.currentMusic.id);
        this.isLiked = false;
      } else {
        await this.musicDataService.likeSong(this.currentMusic.id);
        this.isLiked = true;
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
    }
  }

  /**
   * 收藏/取消收藏
   */
  private async onFavoriteClick(): Promise<void> {
    if (!this.currentMusic) return;

    try {
      if (this.isFavorite) {
        await this.musicDataService.unfavoriteSong(this.currentMusic.id);
        this.isFavorite = false;
      } else {
        await this.musicDataService.favoriteSong(this.currentMusic.id);
        this.isFavorite = true;
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
    }
  }

  /**
   * 播放模式切换
   */
  private async onPlayModeClick(): Promise<void> {
    const modes = [PlayMode.SEQUENCE, PlayMode.LOOP, PlayMode.SINGLE, PlayMode.RANDOM];
    const currentIndex = modes.indexOf(this.playMode);
    this.playMode = modes[(currentIndex + 1) % modes.length];

    this.musicPlayer.setPlayMode(this.playMode);
    await this.musicDataService.setPlayMode(this.playMode);
  }

  /**
   * 进度条拖拽
   */
  private async onSeekTo(position: number): Promise<void> {
    try {
      await this.musicPlayer.seekTo(position);
    } catch (error) {
      console.error('跳转播放位置失败:', error);
    }
  }

  // ==================== 辅助方法 ====================

  /**
   * 格式化时间显示
   */
  private formatTime(milliseconds: number): string {
    return MusicDataService.formatTime(milliseconds);
  }

  /**
   * 获取播放状态文本
   */
  private getPlayStateText(): string {
    switch (this.playState) {
      case PlayState.PLAYING:
        return '正在播放';
      case PlayState.PAUSED:
        return '已暂停';
      case PlayState.STOPPED:
        return '已停止';
      case PlayState.LOADING:
        return '加载中';
      default:
        return '未知状态';
    }
  }

  /**
   * 获取播放模式文本
   */
  private getPlayModeText(): string {
    switch (this.playMode) {
      case PlayMode.SEQUENCE:
        return '顺序播放';
      case PlayMode.LOOP:
        return '列表循环';
      case PlayMode.SINGLE:
        return '单曲循环';
      case PlayMode.RANDOM:
        return '随机播放';
      default:
        return '顺序播放';
    }
  }

  /**
   * 构建平板优化的主界面
   */
  build() {
    // 使用Tabs组件创建标签页布局，针对平板优化
    Tabs({ barPosition: BarPosition.End }) {
      // 第一个标签页：主页
      TabContent() {
        // 使用Column组件创建垂直布局
        Column() {
          // 顶部区域：时间显示和温度控制（平板横向布局优化）
          Row() {
            // 左侧温度控制区域
            Column() {
              Text("左侧温度")
                .fontSize(22)  // 平板字体加大
                .fontColor(Color.White)
                .margin({ bottom: 18 })

              // 温度滑块控制
              Slider({
                value: this.sliderValue,
                min: 16,
                max: 32,
                step: 1,
                style: SliderStyle.OutSet
              })
                .blockColor('#36D')
                .trackColor('#007DFF')
                .selectedColor('#007DFF')
                .width(200)  // 平板滑块加宽
                .height(50)  // 平板滑块加高
                .onChange((value: number) => {
                  this.sliderValue = value;
                })

              Text(`${this.sliderValue}°C`)
                .fontSize(20)  // 平板字体加大
                .fontColor(Color.White)
                .margin({ top: 10 })
            }
            .alignItems(HorizontalAlign.Center)
            .margin({ left: 50 })  // 平板边距加大

            // 中间时间显示区域
            Column() {
              Text(this.timeStr)
                .fontSize(40)  // 平板时间字体显著加大
                .fontColor(Color.White)
                .fontWeight(FontWeight.Bold)
                .textAlign(TextAlign.Center)
            }
            .layoutWeight(1)
            .justifyContent(FlexAlign.Center)

            // 右侧温度控制区域
            Column() {
              Text("右侧温度")
                .fontSize(22)  // 平板字体加大
                .fontColor(Color.White)
                .margin({ bottom: 18 })

              // 温度滑块控制
              Slider({
                value: this.sliderValue1,
                min: 16,
                max: 32,
                step: 1,
                style: SliderStyle.OutSet
              })
                .blockColor('#36D')
                .trackColor('#007DFF')
                .selectedColor('#007DFF')
                .width(200)  // 平板滑块加宽
                .height(50)  // 平板滑块加高
                .onChange((value: number) => {
                  this.sliderValue1 = value;
                })

              Text(`${this.sliderValue1}°C`)
                .fontSize(20)  // 平板字体加大
                .fontColor(Color.White)
                .margin({ top: 10 })
            }
            .alignItems(HorizontalAlign.Center)
            .margin({ right: 50 })  // 平板边距加大
          }
          .width('100%')
          .height(180)  // 平板顶部区域加高
          .justifyContent(FlexAlign.SpaceBetween)
          .alignItems(VerticalAlign.Center)
          .backgroundColor('#1a1a1a')
          .padding({ top: 40, bottom: 40 })  // 平板内边距加大

          // 音乐播放区域（简化版平板优化）
          this.buildSimpleMusicPlayer()
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#000000')
      }
      .tabBar(this.imageBuilder("app.media.car"))

      // 第二个标签页：音乐
      TabContent() {
        Column() {
          Text("音乐播放列表")
            .fontSize(32)  // 平板标题字体加大
            .fontColor(Color.White)
            .margin({ top: 40, bottom: 30 })

          Text("平板音乐功能开发中...")
            .fontSize(18)
            .fontColor('#CCCCCC')
            .textAlign(TextAlign.Center)
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#000000')
        .justifyContent(FlexAlign.Center)
      }
      .tabBar(this.imageBuilder("app.media.yinyue"))

      // 第三个标签页：导航
      TabContent() {
        Column() {
          Text("导航功能")
            .fontSize(32)  // 平板字体加大
            .fontColor(Color.White)
            .textAlign(TextAlign.Center)
            .margin({ top: 100 })

          Text("导航功能开发中...")
            .fontSize(22)  // 平板字体加大
            .fontColor(Color.Gray)
            .textAlign(TextAlign.Center)
            .margin({ top: 40 })
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#000000')
        .justifyContent(FlexAlign.Center)
      }
      .tabBar(this.imageBuilder("app.media.ditu"))

      // 第四个标签页：设置
      TabContent() {
        Column() {
          Text("系统设置")
            .fontSize(32)  // 平板字体加大
            .fontColor(Color.White)
            .textAlign(TextAlign.Center)
            .margin({ top: 100 })

          Text("设置功能开发中...")
            .fontSize(22)  // 平板字体加大
            .fontColor(Color.Gray)
            .textAlign(TextAlign.Center)
            .margin({ top: 40 })
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#000000')
        .justifyContent(FlexAlign.Center)
      }
      .tabBar(this.imageBuilder("app.media.shezhi"))
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
    .barBackgroundColor('#1a1a1a')
    .barMode(BarMode.Fixed)
    .barHeight(100)  // 平板底部标签栏加高
  }

  /**
   * 构建简化的音乐播放器（平板优化）
   */
  @Builder
  buildSimpleMusicPlayer() {
    Column() {
      // 音乐信息显示区域
      Row() {
        // 封面图片
        if (this.currentMusic) {
          if (this.currentMusic.isNetworkResource) {
            Image(this.currentMusic.coverUri)
              .width(120)
              .height(120)
              .borderRadius(16)
              .alt($r('app.media.yinyue'))
          } else {
            Image($r(this.currentMusic.coverUri))
              .width(120)
              .height(120)
              .borderRadius(16)
          }
        } else {
          Image($r('app.media.yinyue'))
            .width(120)
            .height(120)
            .borderRadius(16)
        }

        // 音乐信息
        Column() {
          Text(this.currentMusic?.title || '未选择音乐')
            .fontSize(24)
            .fontColor(Color.White)
            .fontWeight(FontWeight.Bold)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .width('100%')
            .textAlign(TextAlign.Start)

          Text(this.currentMusic?.artist || '未知艺术家')
            .fontSize(18)
            .fontColor('#CCCCCC')
            .margin({ top: 8 })
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .width('100%')
            .textAlign(TextAlign.Start)

          Text(this.getPlayStateText())
            .fontSize(14)
            .fontColor('#007DFF')
            .margin({ top: 10 })
        }
        .layoutWeight(1)
        .margin({ left: 30 })
        .alignItems(HorizontalAlign.Start)
      }
      .width('100%')
      .padding({ left: 40, right: 40, top: 20 })
      .justifyContent(FlexAlign.Start)
      .alignItems(VerticalAlign.Center)

      // 控制按钮区域
      Row() {
        // 上一首按钮
        Button() {
          Image($r('app.media.tui'))
            .width(32)
            .height(32)
            .fillColor('#FFFFFF')
        }
        .width(70)
        .height(70)
        .backgroundColor('#333333')
        .borderRadius(35)
        .onClick(() => this.onPreviousClick())

        // 播放/暂停按钮
        Button() {
          Image($r('app.media.bo'))
            .width(40)
            .height(40)
            .fillColor('#FFFFFF')
        }
        .width(90)
        .height(90)
        .backgroundColor('#007DFF')
        .borderRadius(45)
        .margin({ left: 30, right: 30 })
        .onClick(() => this.onPlayPauseClick())

        // 下一首按钮
        Button() {
          Image($r('app.media.jin'))
            .width(32)
            .height(32)
            .fillColor('#FFFFFF')
        }
        .width(70)
        .height(70)
        .backgroundColor('#333333')
        .borderRadius(35)
        .onClick(() => this.onNextClick())
      }
      .width('100%')
      .padding({ left: 40, right: 40, top: 30, bottom: 20 })
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)

      // 播放模式显示
      Text(`播放模式: ${this.getPlayModeText()}`)
        .fontSize(16)
        .fontColor('#007DFF')
        .margin({ bottom: 20 })
        .onClick(() => this.onPlayModeClick())
    }
    .width('100%')
    .backgroundColor('#1a1a1a')
    .borderRadius(20)
    .margin({ left: 30, right: 30, top: 20, bottom: 20 })
    .padding({ top: 20, bottom: 20 })
  }
}
