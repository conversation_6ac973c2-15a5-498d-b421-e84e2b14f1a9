/**
 * 网络音乐功能测试
 * 用于验证网络音乐加载和播放功能
 */

import { KugouMusicService } from '../services/KugouMusicService';
import { MusicDataService } from '../services/MusicDataService';
import { MusicInfo } from '../common/MusicTypes';

export class NetworkMusicTest {
  
  /**
   * 测试网络连接
   */
  public static async testNetworkConnection(): Promise<boolean> {
    console.log('🔍 测试网络连接...');
    try {
      const isConnected = await KugouMusicService.checkNetworkConnection();
      console.log(`✅ 网络连接状态: ${isConnected ? '已连接' : '未连接'}`);
      return isConnected;
    } catch (error) {
      console.error('❌ 网络连接测试失败:', error);
      return false;
    }
  }

  /**
   * 测试获取示例网络音乐列表
   */
  public static testExampleNetworkMusicList(): MusicInfo[] {
    console.log('🎵 测试获取示例网络音乐列表...');
    try {
      const musicList = KugouMusicService.getExampleNetworkMusicList();
      console.log(`✅ 获取到 ${musicList.length} 首示例音乐`);
      
      musicList.forEach((music, index) => {
        console.log(`${index + 1}. ${music.title} - ${music.artist}`);
        console.log(`   封面: ${music.coverUri}`);
        console.log(`   音频: ${music.uri}`);
        console.log(`   网络资源: ${music.isNetworkResource ? '是' : '否'}`);
      });
      
      return musicList;
    } catch (error) {
      console.error('❌ 获取示例音乐列表失败:', error);
      return [];
    }
  }

  /**
   * 测试音乐搜索功能
   */
  public static async testMusicSearch(keyword: string = '流行'): Promise<MusicInfo[]> {
    console.log(`🔍 测试音乐搜索: ${keyword}...`);
    try {
      const musicList = await KugouMusicService.searchMusic(keyword, 1, 5);
      console.log(`✅ 搜索到 ${musicList.length} 首音乐`);
      
      musicList.forEach((music, index) => {
        console.log(`${index + 1}. ${music.title} - ${music.artist}`);
        console.log(`   专辑: ${music.album}`);
        console.log(`   时长: ${Math.floor(music.duration / 1000)}秒`);
      });
      
      return musicList;
    } catch (error) {
      console.error('❌ 音乐搜索测试失败:', error);
      return [];
    }
  }

  /**
   * 测试获取热门音乐
   */
  public static async testHotMusic(): Promise<MusicInfo[]> {
    console.log('🔥 测试获取热门音乐...');
    try {
      const musicList = await KugouMusicService.getHotMusic();
      console.log(`✅ 获取到 ${musicList.length} 首热门音乐`);
      
      musicList.forEach((music, index) => {
        console.log(`${index + 1}. ${music.title} - ${music.artist}`);
      });
      
      return musicList;
    } catch (error) {
      console.error('❌ 获取热门音乐测试失败:', error);
      return [];
    }
  }

  /**
   * 测试MusicDataService的网络功能
   */
  public static async testMusicDataService(): Promise<void> {
    console.log('🎛️ 测试MusicDataService网络功能...');
    try {
      const musicDataService = new MusicDataService();
      
      // 测试获取默认音乐列表
      const defaultList = musicDataService.getDefaultMusicList();
      console.log(`✅ 默认音乐列表: ${defaultList.length} 首`);
      
      // 测试网络连接检查
      const isConnected = await musicDataService.checkNetworkConnection();
      console.log(`✅ 网络连接检查: ${isConnected ? '成功' : '失败'}`);
      
      if (isConnected) {
        // 测试搜索网络音乐
        const searchResults = await musicDataService.searchNetworkMusic('测试', 1, 3);
        console.log(`✅ 搜索网络音乐: ${searchResults.length} 首`);
        
        // 测试获取热门音乐
        const hotMusic = await musicDataService.getHotNetworkMusic();
        console.log(`✅ 热门网络音乐: ${hotMusic.length} 首`);
      }
      
    } catch (error) {
      console.error('❌ MusicDataService测试失败:', error);
    }
  }

  /**
   * 测试封面URL格式
   */
  public static testCoverUrlFormat(): void {
    console.log('🖼️ 测试封面URL格式...');
    try {
      const testHashes = [
        '20200620072010703593',
        '20200620072010703594',
        '20200620072010703595'
      ];
      
      testHashes.forEach((hash, index) => {
        const coverUrl = KugouMusicService.getKugouCoverUrl(hash, 400);
        console.log(`${index + 1}. 封面URL: ${coverUrl}`);
      });
      
      console.log('✅ 封面URL格式测试完成');
    } catch (error) {
      console.error('❌ 封面URL格式测试失败:', error);
    }
  }

  /**
   * 运行所有测试
   */
  public static async runAllTests(): Promise<void> {
    console.log('🚀 开始运行网络音乐功能测试...');
    console.log('='.repeat(50));
    
    try {
      // 1. 测试网络连接
      await this.testNetworkConnection();
      console.log('-'.repeat(30));
      
      // 2. 测试示例音乐列表
      this.testExampleNetworkMusicList();
      console.log('-'.repeat(30));
      
      // 3. 测试封面URL格式
      this.testCoverUrlFormat();
      console.log('-'.repeat(30));
      
      // 4. 测试MusicDataService
      await this.testMusicDataService();
      console.log('-'.repeat(30));
      
      // 5. 测试音乐搜索（如果网络可用）
      const isConnected = await KugouMusicService.checkNetworkConnection();
      if (isConnected) {
        await this.testMusicSearch('流行');
        console.log('-'.repeat(30));
        
        await this.testHotMusic();
        console.log('-'.repeat(30));
      } else {
        console.log('⚠️ 网络不可用，跳过在线测试');
      }
      
      console.log('='.repeat(50));
      console.log('✅ 所有测试完成！');
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
    }
  }

  /**
   * 验证音乐信息格式
   */
  public static validateMusicInfo(music: MusicInfo): boolean {
    const requiredFields = ['id', 'title', 'artist', 'duration', 'uri'];
    const missingFields = requiredFields.filter(field => !music[field]);
    
    if (missingFields.length > 0) {
      console.error(`❌ 音乐信息缺少必要字段: ${missingFields.join(', ')}`);
      return false;
    }
    
    if (music.isNetworkResource && !music.uri.startsWith('http')) {
      console.error('❌ 网络资源的URI必须是HTTP/HTTPS URL');
      return false;
    }
    
    if (music.coverUri && music.isNetworkResource && !music.coverUri.startsWith('http')) {
      console.error('❌ 网络资源的封面URI必须是HTTP/HTTPS URL');
      return false;
    }
    
    console.log(`✅ 音乐信息格式验证通过: ${music.title}`);
    return true;
  }

  /**
   * 批量验证音乐列表
   */
  public static validateMusicList(musicList: MusicInfo[]): boolean {
    console.log(`🔍 验证音乐列表 (${musicList.length} 首)...`);
    
    let validCount = 0;
    musicList.forEach((music, index) => {
      console.log(`验证第 ${index + 1} 首: ${music.title}`);
      if (this.validateMusicInfo(music)) {
        validCount++;
      }
    });
    
    const isValid = validCount === musicList.length;
    console.log(`✅ 验证结果: ${validCount}/${musicList.length} 首音乐格式正确`);
    
    return isValid;
  }
}
