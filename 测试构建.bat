@echo off
chcp 65001 >nul
echo ==========================================
echo    HarmonyOS 平板项目构建测试
echo ==========================================
echo.

:: 检查当前目录
echo [1/4] 检查项目结构...
if not exist "entry\src\main\module.json5" (
    echo ❌ 错误：当前目录不是有效的HarmonyOS项目
    echo 请在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查关键文件
echo [2/4] 检查关键文件...
set "missing_files="

if not exist "entry\src\main\ets\pages\SmartIndex.ets" (
    set "missing_files=%missing_files% SmartIndex.ets"
)

if not exist "entry\src\main\ets\common\DeviceUtils.ets" (
    set "missing_files=%missing_files% DeviceUtils.ets"
)

if not exist "entry\src\main\resources\base\profile\main_pages.json" (
    set "missing_files=%missing_files% main_pages.json"
)

if defined missing_files (
    echo ❌ 缺少关键文件:%missing_files%
    pause
    exit /b 1
) else (
    echo ✅ 所有关键文件都存在
)

:: 检查页面配置
echo [3/4] 检查页面配置...
findstr /c:"SmartIndex" entry\src\main\resources\base\profile\main_pages.json >nul
if errorlevel 1 (
    echo ❌ 错误：main_pages.json 中未配置 SmartIndex 页面
    pause
    exit /b 1
) else (
    echo ✅ SmartIndex 页面配置正确
)

:: 尝试构建项目
echo [4/4] 测试项目构建...
echo 正在安装依赖...
call ohpm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo 正在构建项目...
call hvigorw assembleHap --mode module -p product=default -p buildMode=debug
if errorlevel 1 (
    echo ❌ 项目构建失败
    echo 请检查代码是否有语法错误
    pause
    exit /b 1
) else (
    echo ✅ 项目构建成功！
)

echo.
echo ==========================================
echo    构建测试完成
echo ==========================================
echo ✅ 项目已准备好部署到平板模拟器
echo.
echo 下一步：
echo 1. 启动平板模拟器
echo 2. 运行 "平板模拟器部署.bat" 进行部署
echo.
pause
