@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 HarmonyOS车载娱乐系统 - 快速启动
echo ========================================
echo.

:: 检查当前目录
if not exist "entry" (
    echo ❌ 错误：请在项目根目录运行此脚本
    echo 当前目录：%CD%
    echo 请确保在包含 entry 文件夹的目录中运行
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过
echo 📁 项目路径：%CD%
echo.

:: 检查 ohpm 命令
where ohpm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到 ohpm 命令
    echo 请确保已安装 DevEco Studio 并配置环境变量
    echo.
    echo 💡 解决方案：
    echo 1. 安装 DevEco Studio
    echo 2. 配置 HarmonyOS SDK
    echo 3. 将 SDK 的 toolchains 目录添加到 PATH 环境变量
    pause
    exit /b 1
)

echo ✅ ohpm 命令检查通过
echo.

:: 安装依赖
echo 📦 正在安装项目依赖...
echo.
ohpm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 清理缓存：ohpm cache clean
    echo 3. 删除 oh_modules 文件夹后重试
    pause
    exit /b 1
)

echo ✅ 依赖安装完成
echo.

:: 检查 hvigorw 命令
if not exist "hvigorw.bat" (
    echo ❌ 错误：未找到 hvigorw.bat
    echo 请确保在正确的 HarmonyOS 项目目录中
    pause
    exit /b 1
)

:: 清理项目
echo 🧹 正在清理项目...
call hvigorw.bat clean
if %errorlevel% neq 0 (
    echo ⚠️ 警告：项目清理失败，继续构建...
)

echo ✅ 项目清理完成
echo.

:: 构建项目
echo 🔨 正在构建项目...
echo.
call hvigorw.bat assembleHap
if %errorlevel% neq 0 (
    echo ❌ 项目构建失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查代码语法错误
    echo 2. 确认 SDK 版本兼容性
    echo 3. 查看详细错误信息
    echo 4. 在 DevEco Studio 中打开项目进行调试
    pause
    exit /b 1
)

echo ✅ 项目构建成功
echo.

:: 检查构建产物
set "HAP_FILE=entry\build\default\outputs\default\entry-default-signed.hap"
if not exist "%HAP_FILE%" (
    echo ❌ 错误：未找到构建产物
    echo 预期位置：%HAP_FILE%
    echo.
    echo 💡 请检查构建过程是否有错误
    pause
    exit /b 1
)

echo ✅ 构建产物检查通过
echo 📦 HAP文件：%HAP_FILE%
echo.

:: 检查设备连接
echo 🔍 检查设备连接...
hdc list targets >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：hdc 命令不可用
    echo 请确保 HarmonyOS SDK 工具链已正确安装
    pause
    exit /b 1
)

:: 获取设备列表
for /f "tokens=*" %%i in ('hdc list targets 2^>nul') do (
    set "DEVICE_LIST=%%i"
)

if "%DEVICE_LIST%"=="" (
    echo ⚠️ 警告：未检测到连接的设备
    echo.
    echo 💡 请执行以下操作之一：
    echo 1. 连接 HarmonyOS 设备并启用 USB 调试
    echo 2. 启动 HarmonyOS 模拟器
    echo 3. 在 DevEco Studio 中手动运行项目
    echo.
    echo 📱 HAP 文件已构建完成，位置：
    echo    %HAP_FILE%
    echo.
    echo 🎯 您可以：
    echo    - 在 DevEco Studio 中点击 Run 按钮
    echo    - 手动安装 HAP 文件到设备
    echo    - 使用模拟器运行应用
    pause
    exit /b 0
)

echo ✅ 检测到设备：%DEVICE_LIST%
echo.

:: 安装应用
echo 📱 正在安装应用到设备...
hdc install "%HAP_FILE%"
if %errorlevel% neq 0 (
    echo ❌ 应用安装失败
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查设备连接
    echo 2. 确认设备已启用开发者模式
    echo 3. 检查设备存储空间
    echo 4. 卸载旧版本应用后重试
    pause
    exit /b 1
)

echo ✅ 应用安装成功
echo.

:: 启动应用
echo 🚀 正在启动应用...
hdc shell aa start -a EntryAbility -b com.example.zuoye1
if %errorlevel% neq 0 (
    echo ⚠️ 警告：自动启动失败，请手动启动应用
    echo 📱 请在设备上找到"zuoye1"应用并点击启动
) else (
    echo ✅ 应用启动成功
)

echo.
echo ========================================
echo 🎉 启动完成！
echo ========================================
echo.
echo 📱 应用已安装到设备：%DEVICE_LIST%
echo 🎵 您现在可以使用车载娱乐系统了！
echo.
echo 🎯 主要功能：
echo    ✅ 音乐播放控制
echo    ✅ 网络封面图片加载
echo    ✅ 播放模式切换
echo    ✅ 点赞收藏功能
echo    ✅ 进度控制
echo.
echo 📚 更多信息请查看：
echo    - 🚀项目运行指南.md
echo    - 网络音乐功能使用说明.md
echo    - 🎵网络音乐功能实现完成报告.md
echo.
echo 💡 如遇问题，请：
echo    1. 检查网络连接（网络音乐功能需要）
echo    2. 确认设备权限设置
echo    3. 查看详细文档说明
echo.
pause
